/// <reference types="vite/client" />

// CSS Module declarations
declare module "*.css" {
  const content: Record<string, string>;
  export default content;
}

declare module "*.scss" {
  const content: Record<string, string>;
  export default content;
}

declare module "*.sass" {
  const content: Record<string, string>;
  export default content;
}

declare module "*.less" {
  const content: Record<string, string>;
  export default content;
}

export enum Theme {
  Space = "space",
  Light = "light",
  Dark = "dark",
}

export enum View {
  Dashboard = "dashboard",
  Chat = "chat",
  Grievance = "grievance",
  Confirmed = "confirmed",
}

export enum Role {
  User = "user",
  Model = "model",
}

export interface Message {
  id: string;
  role: Role;
  text: string;
  // New: Optional property to store base64 image data for display in chat log
  imageData?: string; // Base64 string for image display
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
}

export enum GrievanceStatus {
  Pending = "Pending",
  InProgress = "In Progress",
  Resolved = "Resolved",
}

export interface Grievance {
  id: string;
  title: string;
  status: GrievanceStatus;
  date: string;
  category: string;
}
