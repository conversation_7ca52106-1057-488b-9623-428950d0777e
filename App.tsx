import React, { useState, useEffect, useCallback, useRef } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { View, Message, Role, Theme, UserProfile } from "./types";
import {
  streamChatResponse,
  generateReport,
  resetChat,
} from "./services/localLLMService";
import {
  startVoiceRecording,
  stopVoiceRecording,
  isSpeechRecognitionSupported,
  SpeechRecognitionResult,
} from "./utils/speechUtils";
import { GrievanceCanvas } from "./components/RichTextEditor/GrievanceCanvas"; // Corrected import path
import SubmissionModal from "./components/SubmissionModal";
import SpaceBackground from "./components/SpaceUI/SpaceBackground";
import SpaceCursor from "./components/SpaceUI/SpaceCursor";
import SpaceLogo from "./components/SpaceUI/SpaceLogo";
import SpaceText from "./components/SpaceUI/SpaceText";
import ChatInputBar from "./components/ChatInputBar";
import {
  HomeIcon,
  ChatBubbleLeftRightIcon,
  SunIcon,
  MoonIcon,
  StarIcon,
  ChevronDoubleLeftIcon,
  DocumentDuplicateIcon,
  ExclamationTriangleIcon,
} from "./components/icons";

// Helper component for Sidebar navigation
const Sidebar = ({
  currentView,
  setView,
  theme,
  isMinimized,
  toggleMinimize,
}: {
  currentView: View;
  setView: (view: View) => void;
  theme: Theme;
  isMinimized: boolean;
  toggleMinimize: () => void;
}) => {
  const navItems = [
    { view: View.Dashboard, icon: HomeIcon, label: "Dashboard" },
    { view: View.Chat, icon: ChatBubbleLeftRightIcon, label: "New Grievance" },
  ];

  const baseClasses = `flex flex-col transition-all duration-300 relative`;
  const sizeClasses = isMinimized
    ? "w-20 p-3 items-center"
    : "w-64 p-4 items-start";

  let themeClasses,
    activeClasses,
    inactiveClasses,
    iconColor,
    borderColor,
    menuLabelColor;

  switch (theme) {
    case Theme.Space:
      themeClasses = "bg-black/20 backdrop-blur-xl border-r border-white/10";
      activeClasses =
        "bg-purple-500/40 text-white shadow-lg shadow-purple-500/20";
      inactiveClasses = "text-gray-400 hover:bg-white/10 hover:text-white";
      iconColor = "text-purple-300";
      borderColor = "border-white/10";
      menuLabelColor = "text-gray-500";
      break;
    case Theme.Dark:
      themeClasses = "bg-gray-900 border-r border-gray-700";
      activeClasses = "bg-purple-600 text-white shadow-lg shadow-purple-900/40";
      inactiveClasses = "text-gray-400 hover:bg-gray-800 hover:text-white";
      iconColor = "text-purple-400";
      borderColor = "border-gray-700";
      menuLabelColor = "text-gray-500";
      break;
    default: // Light
      themeClasses = "bg-white border-r border-gray-200 shadow-sm";
      activeClasses = "bg-blue-500 text-white shadow-lg shadow-blue-500/30";
      inactiveClasses = "text-gray-600 hover:bg-gray-100 hover:text-blue-600";
      iconColor = "text-blue-500";
      borderColor = "border-gray-200";
      menuLabelColor = "text-gray-400";
      break;
  }

  return (
    <aside className={`${baseClasses} ${sizeClasses} ${themeClasses}`}>
      <div className="flex-grow w-full">
        <div
          className={`h-16 flex items-center mb-6 ${
            isMinimized ? "justify-center" : "justify-start"
          }`}
        >
          <div className="w-10 h-10">
            <ChatBubbleLeftRightIcon className={`w-full h-full ${iconColor}`} />
          </div>
        </div>
        <div className={`mb-4 px-3 ${isMinimized ? "hidden" : ""}`}>
          <span
            className={`text-xs font-semibold tracking-widest uppercase ${menuLabelColor}`}
          >
            Menu
          </span>
        </div>
        <nav className="flex flex-col space-y-3 w-full">
          {navItems.map((item) => {
            const isActive = currentView === item.view;
            return (
              <button
                key={item.view}
                onClick={() => setView(item.view)}
                title={item.label}
                className={`flex items-center space-x-4 p-3 rounded-lg w-full transition-all duration-200 transform hover:scale-105 ${
                  isMinimized ? "justify-center" : ""
                } ${isActive ? activeClasses : inactiveClasses}`}
              >
                <item.icon className="w-6 h-6 flex-shrink-0" />
                <span
                  className={`font-semibold whitespace-nowrap ${
                    isMinimized ? "hidden" : "inline"
                  }`}
                >
                  {item.label}
                </span>
              </button>
            );
          })}
        </nav>
      </div>
      <div
        className={`w-full pt-4 mt-4 ${
          !isMinimized && `border-t ${borderColor}`
        }`}
      >
        <button
          onClick={toggleMinimize}
          title={isMinimized ? "Expand" : "Collapse"}
          className={`flex items-center p-3 rounded-lg w-full transition-colors duration-200 ${inactiveClasses} ${
            isMinimized ? "justify-center" : "justify-start"
          }`}
          aria-label={isMinimized ? "Expand sidebar" : "Collapse sidebar"}
        >
          <ChevronDoubleLeftIcon
            className={`w-5 h-5 flex-shrink-0 transition-transform duration-300 ${
              isMinimized ? "rotate-180" : ""
            }`}
          />
        </button>
      </div>
    </aside>
  );
};

// Helper component for Header
const Header = ({
  title,
  theme,
  toggleTheme,
}: {
  title: string;
  theme: Theme;
  toggleTheme: () => void;
}) => {
  let themeClasses, textClass, iconButtonClass;

  switch (theme) {
    case Theme.Space:
      themeClasses = "bg-black/10 backdrop-blur-md border-b border-white/10";
      textClass = "text-white";
      iconButtonClass = "text-gray-300 hover:bg-white/10 hover:text-white";
      break;
    case Theme.Dark:
      themeClasses = "bg-gray-900 border-b border-gray-700";
      textClass = "text-white";
      iconButtonClass = "text-gray-400 hover:bg-gray-800 hover:text-white";
      break;
    default: // Light
      themeClasses = "bg-white border-b border-gray-200";
      textClass = "text-gray-800";
      iconButtonClass = "text-gray-500 hover:bg-gray-100";
      break;
  }

  const renderThemeIcon = () => {
    switch (theme) {
      case Theme.Light:
        return <MoonIcon className="w-6 h-6" />;
      case Theme.Dark:
        return <StarIcon className="w-6 h-6" />;
      case Theme.Space:
        return <SunIcon className="w-6 h-6" />;
      default:
        return null;
    }
  };

  return (
    <header
      className={`flex justify-between items-center p-4 transition-colors duration-300 ${themeClasses}`}
    >
      <h1 className={`text-2xl font-bold ${textClass}`}>{title}</h1>
      <div className="flex items-center space-x-2">
        <button
          onClick={toggleTheme}
          className={`p-2 rounded-full ${iconButtonClass} transition-colors`}
        >
          {renderThemeIcon()}
        </button>
      </div>
    </header>
  );
};

// ChatLog component - displays messages
const ChatLog = React.memo(
  ({
    messages,
    isLoading,
    chatEndRef,
    theme,
  }: {
    messages: Message[];
    isLoading: boolean;
    chatEndRef: React.RefObject<HTMLDivElement | null>;
    theme: Theme;
  }) => {
    let userBubbleClasses,
      modelBubbleClasses,
      modelIconContainerClasses,
      modelIconClasses;

    switch (theme) {
      case Theme.Space:
        userBubbleClasses =
          "bg-gradient-to-br from-purple-600 to-blue-500 text-white rounded-2xl rounded-br-lg shadow-purple-500/20";
        modelBubbleClasses =
          "bg-white/10 backdrop-blur-md border border-white/10 text-gray-200 rounded-2xl rounded-bl-lg";
        modelIconContainerClasses = "bg-black/20 border border-white/10";
        modelIconClasses = "text-purple-400";
        break;
      case Theme.Dark:
        userBubbleClasses =
          "bg-gradient-to-br from-purple-600 to-blue-500 text-white rounded-2xl rounded-br-lg shadow-purple-500/20";
        modelBubbleClasses =
          "bg-gray-700 text-gray-200 rounded-2xl rounded-bl-lg";
        modelIconContainerClasses = "bg-gray-800 border border-gray-700";
        modelIconClasses = "text-purple-400";
        break;
      default: // Light
        userBubbleClasses = "bg-blue-500 text-white rounded-2xl rounded-br-lg";
        modelBubbleClasses =
          "bg-gray-200 text-gray-800 rounded-2xl rounded-bl-lg";
        modelIconContainerClasses = "bg-gray-200 border border-gray-300";
        modelIconClasses = "text-blue-500";
        break;
    }

    return (
      <div className="flex-grow overflow-y-auto space-y-6">
        {messages.map((msg, index) => (
          <div
            key={msg.id}
            className={`flex items-start gap-3 animate-slideInUp ${
              msg.role === Role.User ? "justify-end" : "justify-start"
            }`}
          >
            {msg.role === Role.Model && (
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${modelIconContainerClasses}`}
              >
                <ChatBubbleLeftRightIcon
                  className={`w-5 h-5 ${modelIconClasses}`}
                />
              </div>
            )}
            <div
              className={`max-w-3xl px-5 py-3 shadow-lg ${
                msg.role === Role.User ? userBubbleClasses : modelBubbleClasses
              }`}
            >
              <p className="whitespace-pre-wrap">
                {msg.text}
                {isLoading &&
                  index === messages.length - 1 &&
                  msg.text === "" && (
                    <span className="inline-flex space-x-1 ml-2">
                      <span
                        className="w-1.5 h-1.5 bg-current rounded-full animate-pulse"
                        style={{ animationDelay: "0s" }}
                      ></span>
                      <span
                        className="w-1.5 h-1.5 bg-current rounded-full animate-pulse"
                        style={{ animationDelay: "0.2s" }}
                      ></span>
                      <span
                        className="w-1.5 h-1.5 bg-current rounded-full animate-pulse"
                        style={{ animationDelay: "0.4s" }}
                      ></span>
                    </span>
                  )}
              </p>
              {/* Display uploaded image if it's a user message with image data */}
              {msg.role === Role.User && msg.imageData && (
                <img
                  src={msg.imageData}
                  alt="Uploaded content"
                  className="mt-2 max-w-full h-auto rounded-lg shadow-md"
                  style={{ maxWidth: "200px" }} // Limit image size for display
                />
              )}
            </div>
          </div>
        ))}
        <div ref={chatEndRef} />
      </div>
    );
  }
);

// Dashboard Content Components
const DashboardCard: React.FC<{
  title: string;
  description: string;
  icon: React.FC<any>;
  theme: Theme;
  onClick: () => void;
}> = ({ title, description, icon: Icon, theme, onClick }) => {
  let cardClasses, titleClasses, descriptionClasses, iconClasses;

  switch (theme) {
    case Theme.Space:
      cardClasses =
        "bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10";
      titleClasses = "text-white";
      descriptionClasses = "text-gray-300";
      iconClasses = "text-purple-400";
      break;
    case Theme.Dark:
      cardClasses = "bg-gray-800 border border-gray-700 hover:bg-gray-700";
      titleClasses = "text-white";
      descriptionClasses = "text-gray-400";
      iconClasses = "text-purple-500";
      break;
    default: // Light
      cardClasses = "bg-white border border-gray-200 hover:bg-gray-50";
      titleClasses = "text-gray-800";
      descriptionClasses = "text-gray-600";
      iconClasses = "text-blue-500";
      break;
  }

  return (
    <button
      onClick={onClick}
      className={`flex flex-col items-center p-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg ${cardClasses}`}
    >
      <Icon className={`w-12 h-12 mb-4 ${iconClasses}`} />
      <h3 className={`text-xl font-semibold mb-2 ${titleClasses}`}>{title}</h3>
      <p className={`text-center ${descriptionClasses}`}>{description}</p>
    </button>
  );
};

const LightDashboardContent: React.FC<{
  onSendMessage: (msg: string) => void;
  theme: Theme;
}> = ({ onSendMessage, theme }) => (
  <div className="flex flex-col items-center justify-center h-full p-4">
    <h2 className="text-4xl font-bold text-gray-800 mb-6 animate-fadeInUp">
      Welcome to CivicAssist
    </h2>
    <p className="text-lg text-gray-600 mb-10 text-center max-w-2xl animate-fadeInUp delay-100">
      Your AI-powered assistant for drafting formal grievance reports and
      managing community concerns.
    </p>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-4xl animate-fadeInUp delay-200">
      <DashboardCard
        title="Start New Grievance"
        description="Begin a guided conversation to draft a new grievance report."
        icon={ChatBubbleLeftRightIcon}
        theme={theme}
        onClick={() => onSendMessage("I need to file a new grievance report.")}
      />
      <DashboardCard
        title="Review Past Grievances"
        description="View your previously submitted reports and their statuses."
        icon={DocumentDuplicateIcon}
        theme={theme}
        onClick={() => alert("Feature coming soon! (Review Past Grievances)")}
      />
      <DashboardCard
        title="Knowledge Base"
        description="Find resources and FAQs about common grievance procedures."
        icon={HomeIcon} // Re-using HomeIcon for now, replace with a suitable icon later
        theme={theme}
        onClick={() => alert("Feature coming soon! (Knowledge Base)")}
      />
    </div>
  </div>
);

const DarkDashboardContent: React.FC<{
  onSendMessage: (msg: string) => void;
  theme: Theme;
}> = ({ onSendMessage, theme }) => (
  <div className="flex flex-col items-center justify-center h-full p-4">
    <h2 className="text-4xl font-bold text-white mb-6 animate-fadeInUp">
      Welcome to CivicAssist
    </h2>
    <p className="text-lg text-gray-300 mb-10 text-center max-w-2xl animate-fadeInUp delay-100">
      Your AI-powered assistant for drafting formal grievance reports and
      managing community concerns.
    </p>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-4xl animate-fadeInUp delay-200">
      <DashboardCard
        title="Start New Grievance"
        description="Begin a guided conversation to draft a new grievance report."
        icon={ChatBubbleLeftRightIcon}
        theme={theme}
        onClick={() => onSendMessage("I need to file a new grievance report.")}
      />
      <DashboardCard
        title="Review Past Grievances"
        description="View your previously submitted reports and their statuses."
        icon={DocumentDuplicateIcon}
        theme={theme}
        onClick={() => alert("Feature coming soon! (Review Past Grievances)")}
      />
      <DashboardCard
        title="Knowledge Base"
        description="Find resources and FAQs about common grievance procedures."
        icon={HomeIcon}
        theme={theme}
        onClick={() => alert("Feature coming soon! (Knowledge Base)")}
      />
    </div>
  </div>
);

const SpaceDashboardContent: React.FC<{
  onSendMessage: (msg: string) => void;
  theme: Theme;
}> = ({ onSendMessage, theme }) => {
  console.log("🔧 SpaceDashboardContent rendering...");
  return (
    <div className="flex flex-col items-center justify-center h-full p-4 text-white relative z-10">
      <SpaceLogo className="w-24 h-24 mb-6 animate-pulse-light" />
      <SpaceText
        type="h1"
        className="text-5xl font-extrabold mb-4 animate-fadeInUp text-center"
      >
        CivicAssist
      </SpaceText>
      <SpaceText
        type="p"
        className="text-lg mb-12 text-center max-w-2xl animate-fadeInUp delay-100"
      >
        Your AI-powered assistant for drafting formal grievance reports and
        managing community concerns.
      </SpaceText>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full max-w-5xl animate-fadeInUp delay-200">
        <DashboardCard
          title="Initiate New Report"
          description="Start a secure, guided process to document your concerns."
          icon={ChatBubbleLeftRightIcon}
          theme={theme}
          onClick={() =>
            onSendMessage("I need to file a new grievance report.")
          }
        />
        <DashboardCard
          title="Access Historical Records"
          description="Review and manage your past grievance submissions."
          icon={DocumentDuplicateIcon}
          theme={theme}
          onClick={() =>
            alert("Feature coming soon! (Access Historical Records)")
          }
        />
        <DashboardCard
          title="Explore Community Insights"
          description="Discover resources and best practices for conflict resolution."
          icon={HomeIcon}
          theme={theme}
          onClick={() =>
            alert("Feature coming soon! (Explore Community Insights)")
          }
        />
      </div>
    </div>
  );
};

// Main App component
export default function App() {
  console.log("🔧 App component rendering...");

  // Core App State Flow
  const [currentView, setCurrentView] = useState<View>(View.Dashboard);
  const [theme, setTheme] = useState<Theme>(Theme.Space);
  const [isSidebarMinimized, setIsSidebarMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);

  // Report Generation State
  const [isLoading, setIsLoading] = useState(false);
  const [isReportGenerating, setIsReportGenerating] = useState(false);
  const [generatedReport, setGeneratedReport] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Enhanced State for Workflow
  const [workflowStage, setWorkflowStage] = useState<
    "dashboard" | "chat" | "grievance" | "confirmed"
  >("dashboard");
  const [userProfile] = useState<UserProfile>({
    firstName: "Jane",
    lastName: "Doe",
    email: "<EMAIL>",
  });

  // Multimodal input states
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  // TypeScript: Define SpeechRecognition type for browser compatibility
  type SpeechRecognition = typeof window.SpeechRecognition extends undefined
    ? typeof window.webkitSpeechRecognition
    : typeof window.SpeechRecognition;

  const speechRecognitionRef = useRef<InstanceType<SpeechRecognition> | null>(
    null
  );
  const [voiceTranscript, setVoiceTranscript] = useState<string>("");

  const chatEndRef = useRef<HTMLDivElement>(null);
  const isGrievanceViewInitialized = useRef(false);

  // Effect for initial application load animation
  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Effect to scroll to the end of the chat/report
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, generatedReport]);

  // Callback to generate or update the grievance report
  const handleGenerateReport = useCallback(async () => {
    setIsReportGenerating(true);
    const report = await generateReport(messages, userProfile, generatedReport);
    setGeneratedReport(report);
    setIsReportGenerating(false);
  }, [messages, generatedReport, userProfile]);

  // Effect to trigger report generation when switching to Grievance view
  useEffect(() => {
    if (
      currentView === View.Grievance &&
      !isGrievanceViewInitialized.current &&
      messages.length > 0
    ) {
      handleGenerateReport();
      isGrievanceViewInitialized.current = true;
    }
  }, [currentView, messages, handleGenerateReport]);

  // Theme toggling logic
  const toggleTheme = () =>
    setTheme((prev) => {
      if (prev === Theme.Light) return Theme.Dark;
      if (prev === Theme.Dark) return Theme.Space;
      return Theme.Light;
    });

  // Sidebar minimization toggling
  const toggleSidebar = () => setIsSidebarMinimized((prev) => !prev);

  // Handles view changes and resets chat state if moving to new grievance
  const setView = (view: View) => {
    if (view === View.Chat && currentView !== View.Chat) {
      resetChat(); // Reset chat history in localLLMService
      setMessages([
        {
          id: "initial-bot-message",
          role: Role.Model,
          text: "Hello! I’m CivicAssist. How can I help you with your grievance today?",
        },
      ]);
      setGeneratedReport("");
      isGrievanceViewInitialized.current = false;
    }
    if (view === View.Dashboard) {
      setMessages([]);
      resetChat(); // Reset chat history in localLLMService
    }
    setCurrentView(view);
  };

  // Callback to switch to Grievance view (triggered by LLM tool call)
  const switchToGrievanceView = useCallback((toolCallData?: any) => {
    console.log(
      "🔧 switchToGrievanceView called - switching to Grievance view"
    );

    // Check if this is an UpdateTool call
    if (toolCallData?.updateData) {
      const { section, content, action } = toolCallData.updateData;
      console.log("🔧 Processing UpdateTool:", { section, content, action });

      // Apply the update to the current report
      handleUpdateSection(section, content, action);
    } else {
      // Regular grievance view switch
      setCurrentView(View.Grievance);
      setWorkflowStage("grievance");
    }
  }, []);

  // Handle UpdateTool section updates
  const handleUpdateSection = useCallback(
    (
      sectionTitle: string,
      content: string,
      action: "append" | "replace" = "append"
    ) => {
      setGeneratedReport((prevReport) => {
        const sectionRegex = new RegExp(
          `(#{1,6}\\s*${sectionTitle}.*?)(?=#{1,6}|$)`,
          "gis"
        );
        const match = prevReport.match(sectionRegex);

        if (match) {
          if (action === "replace") {
            return prevReport.replace(
              sectionRegex,
              `## ${sectionTitle}\n\n${content}\n\n`
            );
          } else {
            return prevReport.replace(
              sectionRegex,
              `${match[0]}\n\n${content}\n\n`
            );
          }
        } else {
          // Section doesn't exist, append at the end
          return `${prevReport}\n\n## ${sectionTitle}\n\n${content}\n\n`;
        }
      });
    },
    []
  );

  // --- Multimodal Input Handlers ---

  // Handles file selection from ChatInputBar
  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    // File selection is now handled by ChatInputBar component
  }, []);

  // Starts audio recording with speech recognition
  const handleAudioRecordStart = useCallback(async () => {
    if (isRecording) return; // Already recording

    // Check if speech recognition is supported
    if (!isSpeechRecognitionSupported()) {
      alert(
        "Speech recognition is not supported in this browser. Please use Chrome or Edge."
      );
      return;
    }

    try {
      // Start speech recognition for real-time transcription
      speechRecognitionRef.current = startVoiceRecording(
        (result: SpeechRecognitionResult) => {
          console.log("🎙️ Voice transcript:", result.transcript);
          setVoiceTranscript((prev) => prev + " " + result.transcript);
        },
        (error: string) => {
          console.error("Speech recognition error:", error);
          setIsRecording(false);
        },
        {
          continuous: true,
          interimResults: false,
          language: "en-US",
        }
      );

      if (speechRecognitionRef.current) {
        setIsRecording(true);
        setVoiceTranscript(""); // Clear previous transcript
      }

      // Also start traditional audio recording as backup
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      const audioChunks: Blob[] = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(audioChunks, { type: "audio/webm" });
        setAudioBlob(blob);
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorderRef.current.start();
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert(
        "Could not access microphone. Please ensure permissions are granted."
      );
      setIsRecording(false);
    }
  }, [isRecording]);

  // Stops audio recording and speech recognition
  const handleAudioRecordStop = useCallback(() => {
    // Stop speech recognition
    if (speechRecognitionRef.current) {
      stopVoiceRecording(speechRecognitionRef.current);
      speechRecognitionRef.current = null;

      // Send the transcribed text if available
      if (voiceTranscript.trim()) {
        console.log("🎙️ Sending voice transcript:", voiceTranscript.trim());
        // We'll handle sending the transcript in the parent component
        // For now, just log it - the actual sending will be handled elsewhere
        setVoiceTranscript("");
      }
    }

    // Stop traditional audio recording
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      mediaRecorderRef.current.stop();
    }

    setIsRecording(false);
  }, [voiceTranscript]);

  // Helper function to encapsulate message sending logic
  const processMessageAndSend = useCallback(
    async (
      text: string,
      fileData?: { mimeType: string; base64Data: string },
      audioData?: Blob
    ) => {
      const newUserMessage: Message = {
        id: Date.now().toString(),
        role: Role.User,
        text: text,
        // Attach image data to the message for display in ChatLog
        imageData: fileData?.mimeType.startsWith("image/")
          ? `data:${fileData.mimeType};base64,${fileData.base64Data}`
          : undefined,
      };
      setMessages((prev) => [...prev, newUserMessage]);

      const modelMessageId = (Date.now() + 1).toString();
      setMessages((prev) => [
        ...prev,
        { id: modelMessageId, role: Role.Model, text: "" },
      ]);

      try {
        await streamChatResponse(
          text,
          userProfile,
          (chunk) => {
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === modelMessageId
                  ? { ...msg, text: msg.text + chunk }
                  : msg
              )
            );
          },
          switchToGrievanceView,
          fileData, // Pass file data to LLM service
          audioData // Pass audio data to LLM service (for STT or direct multimodal)
        );
      } catch (error) {
        console.error("Error streaming chat response:", error);
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === modelMessageId
              ? {
                  ...msg,
                  text: msg.text + "\n\nError: Could not get response.",
                }
              : msg
          )
        );
      } finally {
        setIsLoading(false);
      }
    },
    [userProfile, switchToGrievanceView]
  );

  // Handles sending user messages (text, file, or audio)
  const handleSendMessage = useCallback(
    async (messageText: string) => {
      console.log("🔧 handleSendMessage called with:", messageText);
      if (isLoading) return; // Prevent multiple sends while loading
      let fileData: { mimeType: string; base64Data: string } | undefined;
      let audioData: Blob | undefined;

      setIsLoading(true);

      // Handle file input
      if (selectedFile) {
        const reader = new FileReader();
        reader.onloadend = async () => {
          const base64String = reader.result as string;
          fileData = {
            mimeType: selectedFile.type,
            base64Data: base64String.split(",")[1], // Get base64 content
          };
          // Once file is processed, proceed with message sending
          await processMessageAndSend(messageText, fileData, audioData);
          setSelectedFile(null); // Clear file after sending
        };
        reader.onerror = (error) => {
          console.error("Error reading file:", error);
          alert("Failed to read the selected file."); // Replace with custom modal
          setIsLoading(false);
        };
        reader.readAsDataURL(selectedFile); // Read file as Data URL for base64
      } else if (audioBlob) {
        // If audio is present, first transcribe it (simulated here for now)
        // In a real app, you'd send audioBlob to a STT service
        // For now, we'll just set a placeholder text and then send.
        messageText = "Transcribing audio..."; // This will be replaced by actual transcription
        audioData = audioBlob; // Pass the blob directly for STT on service side if needed

        await processMessageAndSend(messageText, fileData, audioData);
        setAudioBlob(null); // Clear audio after sending
      } else if (messageText) {
        await processMessageAndSend(messageText, fileData, audioData);
      } else {
        setIsLoading(false); // No input to send
        return;
      }
    },
    [isLoading, selectedFile, audioBlob, processMessageAndSend]
  );

  // Handles starting a new chat from the dashboard
  const handleStartChat = useCallback(
    async (initialMessage: string) => {
      if (!initialMessage.trim() || isLoading) return;

      setView(View.Chat); // This will reset chat state

      // Use a small timeout to allow state updates to propagate
      setTimeout(async () => {
        await processMessageAndSend(initialMessage); // Use the unified send function
      }, 0);
    },
    [isLoading, processMessageAndSend]
  );

  // Handles modal confirmation for report submission
  const handleModalConfirm = () => {
    setView(View.Confirmed);
    setIsModalOpen(false);
  };

  // Renders content based on the current view
  const renderContent = () => {
    console.log(
      "🔧 renderContent called with currentView:",
      currentView,
      "theme:",
      theme
    );
    switch (currentView) {
      case View.Dashboard: {
        let dashboardContent;
        if (theme === Theme.Space)
          dashboardContent = (
            <SpaceDashboardContent
              onSendMessage={handleStartChat}
              theme={theme}
            />
          );
        else if (theme === Theme.Dark)
          dashboardContent = (
            <DarkDashboardContent
              onSendMessage={handleStartChat}
              theme={theme}
            />
          );
        else
          dashboardContent = (
            <LightDashboardContent
              onSendMessage={handleStartChat}
              theme={theme}
            />
          );
        return (
          <div className="w-full h-full max-w-7xl mx-auto p-4 lg:p-6">
            {dashboardContent}
          </div>
        );
      }
      case View.Chat: {
        let chatPanelClasses;
        switch (theme) {
          case Theme.Space:
            chatPanelClasses =
              "bg-black/10 backdrop-blur-md border border-white/10";
            break;
          case Theme.Dark:
            chatPanelClasses = "bg-gray-900 border border-gray-700";
            break;
          default: // Light
            chatPanelClasses = "bg-white border border-gray-200";
            break;
        }

        return (
          <div className="w-full h-full p-4 lg:p-6 flex justify-center items-center">
            <div
              className={`w-full max-w-4xl h-full flex flex-col rounded-2xl p-4 ${chatPanelClasses}`}
            >
              <ChatLog
                messages={messages}
                isLoading={isLoading}
                chatEndRef={chatEndRef}
                theme={theme}
              />
              <ChatInputBar
                onSubmit={handleSendMessage}
                placeholder="Continue the conversation..."
                theme={theme}
                onFileSelect={handleFileSelect}
                onAudioRecordStart={handleAudioRecordStart}
                onAudioRecordStop={handleAudioRecordStop}
                isRecording={isRecording}
                isDisabled={isLoading}
              />
            </div>
          </div>
        );
      }
      case View.Grievance: {
        let panelBgClasses;
        switch (theme) {
          case Theme.Space:
            panelBgClasses = "bg-black/20 backdrop-blur-xl border-white/10";
            break;
          case Theme.Dark:
            panelBgClasses = "bg-[#1e1e2f]";
            break;
          default: // Light
            panelBgClasses = "bg-white border border-gray-200";
            break;
        }

        return (
          <div className="flex flex-col lg:flex-row w-full h-full p-4 gap-4">
            {/* Chat Section */}
            <div
              className={`flex flex-col flex-1 lg:flex-[1.2] p-4 rounded-xl shadow-md ${panelBgClasses}`}
            >
              <ChatLog
                messages={messages}
                isLoading={isLoading}
                chatEndRef={chatEndRef}
                theme={theme}
              />
              <ChatInputBar
                onSubmit={handleSendMessage}
                placeholder="Refine the draft with more details..."
                theme={theme}
                onFileSelect={handleFileSelect}
                onAudioRecordStart={handleAudioRecordStart}
                onAudioRecordStop={handleAudioRecordStop}
                isRecording={isRecording}
                isDisabled={isLoading || isReportGenerating}
              />
            </div>

            {/* Drafting Section */}
            <div
              className={`flex flex-col flex-1 p-0 rounded-xl shadow-md ${panelBgClasses} overflow-hidden`}
            >
              <GrievanceCanvas
                value={generatedReport}
                onChange={setGeneratedReport}
                isGenerating={isReportGenerating}
                onUpdateDraft={handleGenerateReport}
                onSubmit={() => setIsModalOpen(true)}
                theme={theme}
                userProfile={userProfile}
                onUpdateSection={handleUpdateSection}
              />
            </div>
          </div>
        );
      }
      case View.Confirmed: {
        let confirmedTextClasses, confirmedSubTextClasses;
        switch (theme) {
          case Theme.Space:
            confirmedTextClasses =
              "text-transparent bg-clip-text bg-gradient-to-r from-green-300 via-cyan-300 to-green-300 animate-glow";
            confirmedSubTextClasses = "text-gray-300";
            break;
          case Theme.Dark:
            confirmedTextClasses = "text-green-400";
            confirmedSubTextClasses = "text-gray-400";
            break;
          default: // Light
            confirmedTextClasses = "text-green-500";
            confirmedSubTextClasses = "text-gray-600";
            break;
        }

        const confirmedContent = (
          <div className="w-full h-full flex flex-col items-center justify-center p-4 animate-fadeIn text-center">
            <h2 className={`text-5xl font-bold ${confirmedTextClasses}`}>
              Grievance Submitted!
            </h2>
            <p className={`text-lg ${confirmedSubTextClasses} mt-4`}>
              Thank you. Your report has been successfully recorded.
            </p>
            <button
              onClick={() => setView(View.Dashboard)}
              className="mt-8 px-8 py-3 font-semibold rounded-lg text-white bg-gradient-to-r from-purple-600 to-blue-500 hover:shadow-lg hover:shadow-purple-500/40 transition-all duration-300"
            >
              Return to Dashboard
            </button>
          </div>
        );
        return (
          <div className="w-full h-full max-w-7xl mx-auto p-4 lg:p-6">
            {confirmedContent}
          </div>
        );
      }
      default: {
        let dashboardContent;
        if (theme === Theme.Space)
          dashboardContent = (
            <SpaceDashboardContent
              onSendMessage={handleStartChat}
              theme={theme}
            />
          );
        else if (theme === Theme.Dark)
          dashboardContent = (
            <DarkDashboardContent
              onSendMessage={handleStartChat}
              theme={theme}
            />
          );
        else
          dashboardContent = (
            <LightDashboardContent
              onSendMessage={handleStartChat}
              theme={theme}
            />
          );
        return (
          <div className="w-full h-full max-w-7xl mx-auto p-4 lg:p-6">
            {dashboardContent}
          </div>
        );
      }
    }
  };

  let mainContainerClasses;
  switch (theme) {
    case Theme.Space:
      mainContainerClasses =
        "font-sans text-gray-200 selection:bg-purple-500/30";
      break;
    case Theme.Dark:
      mainContainerClasses =
        "font-sans text-gray-300 selection:bg-purple-500/30 bg-gray-900";
      break;
    default: // Light
      mainContainerClasses =
        "font-sans text-gray-800 selection:bg-blue-200 bg-gray-100";
      break;
  }

  const modalBackdropClasses =
    theme === Theme.Space || theme === Theme.Dark
      ? "bg-black/50"
      : "bg-gray-900/50";
  let modalPanelClasses,
    modalTextClasses,
    modalSubtleTextClasses,
    modalCancelButton;
  switch (theme) {
    case Theme.Space:
      modalPanelClasses =
        "bg-black/40 backdrop-blur-xl border border-white/10 shadow-2xl shadow-purple-500/20";
      modalTextClasses = "text-white";
      modalSubtleTextClasses = "text-gray-300";
      modalCancelButton = "bg-white/10 hover:bg-white/20 text-white";
      break;
    case Theme.Dark:
      modalPanelClasses =
        "bg-gray-800 border border-gray-700 shadow-2xl shadow-purple-900/20";
      modalTextClasses = "text-white";
      modalSubtleTextClasses = "text-gray-300";
      modalCancelButton = "bg-gray-700 hover:bg-gray-600 text-white";
      break;
    default: // Light
      modalPanelClasses = "bg-white shadow-xl";
      modalTextClasses = "text-gray-800";
      modalSubtleTextClasses = "text-gray-600";
      modalCancelButton = "bg-gray-200 hover:bg-gray-300 text-gray-800";
      break;
  }

  const viewTitles: Record<View, string> = {
    [View.Dashboard]: "",
    [View.Chat]: "",
    [View.Grievance]: "Draft Report",
    [View.Confirmed]: "Submission Confirmed",
  };

  const title = viewTitles[currentView] || "";

  const getPreviewMarkdown = (
    markdown: string,
    profile: UserProfile
  ): string => {
    if (!markdown) return "";
    let formatted = markdown;
    formatted = formatted.replace(
      /\[Your Full Name\]/gi,
      `${profile.firstName} ${profile.lastName}`
    );
    formatted = formatted.replace(/\[Your Email Address\]/gi, profile.email);
    formatted = formatted.replace(
      /\[Your Contact Number\]/gi,
      `(*************`
    ); // mock phone
    return formatted;
  };

  return (
    <div
      className={`min-h-screen w-full transition-colors duration-300 ${mainContainerClasses}`}
    >
      {theme === Theme.Space && <SpaceBackground isLoaded={isLoaded} />}
      {theme === Theme.Space && <SpaceCursor />}

      <div
        className={`relative z-10 flex h-screen overflow-hidden ${
          isSidebarMinimized ? "" : ""
        }`}
      >
        <Sidebar
          currentView={currentView}
          setView={setView}
          theme={theme}
          isMinimized={isSidebarMinimized}
          toggleMinimize={toggleSidebar}
        />

        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title={title} theme={theme} toggleTheme={toggleTheme} />
          <main className="flex-1 overflow-y-auto">
            <div className="w-full h-full">{renderContent()}</div>
          </main>
        </div>
      </div>

      <SubmissionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onConfirm={handleModalConfirm}
        reportContent={generatedReport}
        userProfile={userProfile}
        theme={theme}
      />
    </div>
  );
}
