import { useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { PencilIcon, EyeIcon, DocumentDuplicateIcon } from "./icons";
import { Theme, UserProfile } from "../types";

interface GrievanceCanvasProps {
  value: string;
  onChange: (value: string) => void;
  isGenerating: boolean;
  onUpdateDraft: () => void;
  onSubmit: () => void;
  theme: Theme;
  userProfile: UserProfile;
}

export const GrievanceCanvas = ({
  value,
  onChange,
  isGenerating,
  onUpdateDraft,
  onSubmit,
  theme,
  userProfile,
}: GrievanceCanvasProps) => {
  const [isPreviewMode, setIsPreviewMode] = useState(true);

  const getPreviewMarkdown = (
    markdown: string,
    profile: UserProfile
  ): string => {
    if (!markdown)
      return "<p class='text-gray-400 italic'>The AI will generate your report preview here once enough information is gathered...</p>";

    let formatted = markdown;
    formatted = formatted.replace(
      /\[Your Full Name\]/gi,
      `${profile.firstName} ${profile.lastName}`
    );
    formatted = formatted.replace(/\[Your Email Address\]/gi, profile.email);
    // A mock phone number, as it's not in the profile
    formatted = formatted.replace(
      /\[Your Contact Number\]/gi,
      `(*************`
    );

    return formatted;
  };

  let headerTextClasses,
    headerSubtleTextClasses,
    updateButtonClasses,
    submitButtonClasses,
    contentContainerClasses,
    toggleButtonClasses,
    toggleActiveClasses,
    toggleInactiveClasses,
    paperClasses,
    textAreaClasses;

  switch (theme) {
    case Theme.Space:
      headerTextClasses = "text-white";
      headerSubtleTextClasses = "text-gray-400";
      updateButtonClasses =
        "bg-white/10 text-purple-300 hover:bg-white/20 hover:text-white";
      submitButtonClasses =
        "bg-gradient-to-r from-purple-600 to-blue-500 hover:shadow-lg hover:shadow-purple-500/40 hover:scale-105";
      contentContainerClasses = "bg-black/20 border-white/10";
      toggleButtonClasses = "bg-gray-900/50 border border-white/10";
      toggleActiveClasses =
        "bg-purple-500/40 text-white shadow-lg shadow-purple-500/20";
      toggleInactiveClasses = "text-gray-400 hover:bg-white/10";
      paperClasses = "bg-gray-900/30 font-serif";
      textAreaClasses =
        "bg-transparent text-gray-200 placeholder-gray-500 focus:outline-none";
      break;
    case Theme.Dark:
      headerTextClasses = "text-white";
      headerSubtleTextClasses = "text-gray-400";
      updateButtonClasses =
        "bg-purple-900/50 text-purple-300 hover:bg-purple-800/50 hover:text-white";
      submitButtonClasses =
        "bg-gradient-to-r from-purple-600 to-blue-500 hover:shadow-lg hover:shadow-purple-500/40 hover:scale-105";
      contentContainerClasses = "bg-[#1e1e2f] border-gray-700";
      toggleButtonClasses = "bg-gray-800 border border-gray-700";
      toggleActiveClasses = "bg-purple-600 text-white shadow-lg";
      toggleInactiveClasses = "text-gray-400 hover:bg-gray-700";
      paperClasses = "bg-gray-800/50 font-serif";
      textAreaClasses =
        "bg-transparent text-gray-300 placeholder-gray-500 focus:outline-none";
      break;
    default: // Light
      headerTextClasses = "text-gray-800";
      headerSubtleTextClasses = "text-gray-500";
      updateButtonClasses = "bg-blue-100 text-blue-600 hover:bg-blue-200";
      submitButtonClasses = "bg-blue-600 hover:bg-blue-700";
      contentContainerClasses = "bg-white border-gray-200";
      toggleButtonClasses = "bg-gray-100 border border-gray-200";
      toggleActiveClasses = "bg-blue-500 text-white shadow-lg";
      toggleInactiveClasses = "text-gray-500 hover:bg-gray-200";
      paperClasses = "bg-gray-50 shadow-inner font-serif";
      textAreaClasses =
        "bg-transparent text-gray-800 placeholder-gray-500 focus:outline-none";
      break;
  }

  const markdownComponents = {
    h2: ({ ...props }: any) => (
      <h2
        className={`text-2xl font-bold mb-4 pb-2 border-b ${
          theme === Theme.Light ? "border-gray-300" : "border-gray-600"
        }`}
        {...props}
      />
    ),
    h3: ({ ...props }: any) => (
      <h3 className="text-xl font-semibold mt-6 mb-3" {...props} />
    ),
    p: ({ ...props }: any) => <p className="mb-4 leading-relaxed" {...props} />,
    ul: ({ ...props }: any) => (
      <ul className="list-disc list-inside mb-4 pl-4 space-y-1" {...props} />
    ),
    li: ({ ...props }: any) => <li className="leading-relaxed" {...props} />,
    strong: ({ ...props }: any) => (
      <strong
        className={`font-semibold ${
          theme === Theme.Light ? "text-gray-900" : "text-white"
        }`}
        {...props}
      />
    ),
  };

  return (
    <div className="h-full w-full flex flex-col p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-4 flex-shrink-0">
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0 p-2 rounded-full bg-opacity-20 bg-gradient-to-br from-purple-500 to-blue-500">
            <DocumentDuplicateIcon
              className={`w-7 h-7 ${
                theme === Theme.Light ? "text-blue-600" : "text-white"
              }`}
            />
          </div>
          <div>
            <h2 className={`text-xl font-bold ${headerTextClasses}`}>
              Drafting Assistant
            </h2>
            <p className={`text-sm ${headerSubtleTextClasses}`}>
              Refine and preview your grievance report.
            </p>
          </div>
        </div>
        <div
          className={`flex p-1 rounded-full text-sm font-semibold ${toggleButtonClasses}`}
        >
          <button
            onClick={() => setIsPreviewMode(false)}
            className={`py-1.5 px-4 rounded-full transition-all duration-300 flex items-center gap-2 ${
              !isPreviewMode ? toggleActiveClasses : toggleInactiveClasses
            }`}
          >
            <PencilIcon className="w-4 h-4" /> Edit
          </button>
          <button
            onClick={() => setIsPreviewMode(true)}
            className={`py-1.5 px-4 rounded-full transition-all duration-300 flex items-center gap-2 ${
              isPreviewMode ? toggleActiveClasses : toggleInactiveClasses
            }`}
          >
            <EyeIcon className="w-4 h-4" /> Preview
          </button>
        </div>
      </div>

      {/* Content */}
      <div
        className={`flex-grow flex flex-col rounded-lg border overflow-hidden relative ${contentContainerClasses}`}
      >
        {isPreviewMode ? (
          <div
            className={`p-2 flex-grow overflow-y-auto ${
              theme === Theme.Light ? "bg-gray-100" : "bg-gray-900/50"
            }`}
          >
            <div
              className={`prose max-w-none p-8 min-h-full rounded ${paperClasses}`}
            >
              <ReactMarkdown
                components={markdownComponents}
                remarkPlugins={[remarkGfm]}
              >
                {getPreviewMarkdown(value, userProfile)}
              </ReactMarkdown>
            </div>
          </div>
        ) : (
          <textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            disabled={isGenerating}
            placeholder="The AI will draft your report here. You can then edit the markdown directly."
            className={`w-full h-full p-4 resize-none font-mono text-sm leading-relaxed ${textAreaClasses}`}
          />
        )}

        {isGenerating && (
          <div
            className={`absolute inset-0 flex items-center justify-center text-center p-4 rounded-lg z-20 ${
              theme === Theme.Light
                ? "bg-white/80 backdrop-blur-sm"
                : "bg-black/80 backdrop-blur-sm"
            }`}
          >
            <div
              className={`${
                theme === Theme.Light ? "text-gray-700" : "text-gray-300"
              } animate-pulse`}
            >
              <PencilIcon
                className={`w-8 h-8 mx-auto mb-2 ${
                  theme === Theme.Light ? "text-blue-500" : "text-purple-400"
                }`}
              />
              <p className="font-semibold">AI is refining the draft...</p>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex-shrink-0 flex items-center gap-4 mt-4">
        <button
          onClick={onUpdateDraft}
          disabled={isGenerating}
          className={`w-1/2 py-3 text-sm font-semibold rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-2 ${updateButtonClasses}`}
        >
          <PencilIcon
            className={`w-5 h-5 ${isGenerating ? "animate-pulse" : ""}`}
          />
          {isGenerating ? "Updating..." : "Update with AI"}
        </button>
        <button
          onClick={onSubmit}
          disabled={!value || isGenerating}
          className={`w-1/2 py-3 font-semibold text-white rounded-lg shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform ${submitButtonClasses}`}
        >
          Confirm & Submit Grievance
        </button>
      </div>
    </div>
  );
};
