
import React, { useState, useEffect } from 'react';
import './SpaceUI.css';
import ChatInputBar from '../ChatInputBar';
import SpaceCursor from './SpaceCursor';
import SpaceBackground from './SpaceBackground';
import Space<PERSON>ogo from './SpaceLogo';
import SpaceText from './SpaceText';
import { Theme } from '../../types';

interface SpaceUIProps {
  onSendMessage: (message: string) => void;
}

const SpaceUI: React.FC<SpaceUIProps> = ({
  onSendMessage
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const question = 'How can I help you today?';
  const description = '';

  // Set loaded state after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`space-ui-container ${isLoaded ? 'loaded' : ''}`}>
      {/* Custom cursor */}
      <SpaceCursor />

      {/* Enhanced background effects */}
      <SpaceBackground isLoaded={isLoaded} />

      <div className="space-ui-content">
        {/* Animated logo */}
        <SpaceLogo />

        {/* Enhanced text with animations */}
        <SpaceText
          question={question}
          description={description}
        />
      </div>

      {/* Chat input bar */}
      <div className="space-chat-input-container">
        <ChatInputBar
          onSubmit={onSendMessage}
          placeholder="Type your message"
          disclaimer="AI may produce inaccurate information. Please verify important details."
          theme={Theme.Space}
        />
      </div>
    </div>
  );
};

export default SpaceUI;
