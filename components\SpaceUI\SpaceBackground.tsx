import React, { useState, useEffect, useRef, useMemo, useCallback, lazy, Suspense } from 'react';
import './SpaceBackground.css';

// Constants for extreme performance optimization
const PARTICLE_COUNT = 25; // Drastically reduced for better performance
const SHAPE_COUNT = 5;     // Drastically reduced for better performance
const DUST_COUNT = 30;     // Drastically reduced for better performance

// Performance monitoring
const ENABLE_PERFORMANCE_MONITORING = false; // Set to true to enable performance logging
const FRAME_RATE_THROTTLE = 24; // Limit animations to 24fps for better performance

// Feature flags for progressive enhancement
const ENABLE_PARALLAX = true;      // Enable/disable parallax effects
const ENABLE_AURORA = true;        // Enable/disable aurora effects
const ENABLE_NEBULA = true;        // Enable/disable nebula particles
const ENABLE_GEOMETRIC = true;     // Enable/disable geometric shapes
const ENABLE_DUST = true;          // Enable/disable cosmic dust
const ENABLE_WAVES = true;         // Enable/disable wave effects
const ENABLE_SHOOTING_STARS = true; // Enable/disable shooting stars

interface SpaceBackgroundProps {
  isLoaded: boolean;
}

const SpaceBackground: React.FC<SpaceBackgroundProps> = ({ isLoaded }) => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    opacity: number;
    color: string;
    speed: number;
    delay: number;
    scale: number;
    rotation: number;
  }>>([]);

  const [geometricShapes, setGeometricShapes] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    opacity: number;
    color: string;
    speed: number;
    delay: number;
    type: 'circle' | 'square' | 'triangle' | 'hexagon';
    rotation: number;
  }>>([]);

  const [dustParticles, setDustParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    opacity: number;
    color: string;
    speed: number;
  }>>([]);

  const backgroundRef = useRef<HTMLDivElement>(null);
  const mousePosition = useRef({ x: 0, y: 0 });
  const rafRef = useRef<number | null>(null);
  const isVisibleRef = useRef(true); // Add missing isVisibleRef

  // Performance monitoring utility
  const performanceMonitor = useMemo(() => {
    return {
      frameCount: 0,
      lastTime: 0,
      fps: 0,
      fpsHistory: [] as number[],

      // Start monitoring performance
      start() {
        if (!ENABLE_PERFORMANCE_MONITORING) return;
        this.lastTime = performance.now();
        this.frameCount = 0;
        this.monitorFrame();
      },

      // Monitor each frame
      monitorFrame() {
        if (!ENABLE_PERFORMANCE_MONITORING) return;
        this.frameCount++;
        const now = performance.now();
        const elapsed = now - this.lastTime;

        if (elapsed >= 1000) { // Update every second
          this.fps = Math.round((this.frameCount * 1000) / elapsed);
          this.fpsHistory.push(this.fps);
          if (this.fpsHistory.length > 10) this.fpsHistory.shift();

          const avgFps = this.fpsHistory.reduce((sum, fps) => sum + fps, 0) / this.fpsHistory.length;
          console.log(`FPS: ${this.fps} | Avg FPS: ${avgFps.toFixed(1)}`);

          this.frameCount = 0;
          this.lastTime = now;
        }

        requestAnimationFrame(() => this.monitorFrame());
      }
    };
  }, []);

  // Advanced throttle function with RAF for smoother animations
  const throttle = useCallback((callback: Function, delay: number) => {
    let lastCall = 0;
    let rafId: number | null = null;

    return function(...args: any[]) {
      const now = performance.now();

      // Skip if component is not visible
      if (!isVisibleRef.current) return;

      if (now - lastCall >= delay) {
        lastCall = now;
        if (rafId) cancelAnimationFrame(rafId);
        rafId = requestAnimationFrame(() => {
          callback(...args);
          rafId = null;
        });
      }
    };
  }, []);

  // Optimized RAF throttling for consistent frame rate
  const throttledRAF = useCallback((callback: FrameRequestCallback): void => {
    const interval = 1000 / FRAME_RATE_THROTTLE;
    const now = performance.now();
    const elapsed = now - (rafRef.current || now);

    // Skip if component is not visible
    if (!isVisibleRef.current) return;

    if (elapsed >= interval) {
      rafRef.current = now - (elapsed % interval);
      callback(now);
    }
  }, []);

  // Memoized function to update layer transforms with optimized performance
  const updateLayerTransforms = useCallback(() => {
    if (!backgroundRef.current || !ENABLE_PARALLAX) return;

    const layers = backgroundRef.current.querySelectorAll('.parallax-layer');
    if (layers.length === 0) return;

    // Use throttled RAF for smoother animations
    throttledRAF(() => {
      layers.forEach((layer: Element) => {
        const speed = parseFloat((layer as HTMLElement).dataset.speed || '0');
        // Reduce parallax sensitivity for better performance
        const xOffset = (mousePosition.current.x - 50) * speed * 0.7;
        const yOffset = (mousePosition.current.y - 50) * speed * 0.7;
        (layer as HTMLElement).style.transform = `translate3d(${xOffset}px, ${yOffset}px, 0)`;
      });
    });
  }, [throttledRAF]);

  // Track mouse movement for parallax effect with advanced throttling
  useEffect(() => {
    if (!isLoaded || !ENABLE_PARALLAX) return;

    // Start performance monitoring
    performanceMonitor.start();

    // Use Intersection Observer to pause animations when not visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          isVisibleRef.current = entry.isIntersecting;
        });
      },
      { threshold: 0.1 } // 10% visibility threshold
    );

    if (backgroundRef.current) {
      observer.observe(backgroundRef.current);
    }

    // Enhanced mouse move handler with better throttling
    const handleMouseMove = throttle((e: MouseEvent) => {
      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;

      // Calculate mouse position as percentage of screen
      mousePosition.current = {
        x: (clientX / innerWidth) * 100,
        y: (clientY / innerHeight) * 100
      };

      updateLayerTransforms();
    }, 1000 / FRAME_RATE_THROTTLE); // Throttle to match our target frame rate

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      observer.disconnect();
      if (rafRef.current) cancelAnimationFrame(rafRef.current);
    };
  }, [isLoaded, throttle, updateLayerTransforms, performanceMonitor, throttledRAF]);

  // Generate particles for the nebula effect using useMemo
  const colors = useMemo(() => [
    'rgba(107, 71, 255, 0.7)',  // Purple
    'rgba(71, 125, 255, 0.7)',  // Blue
    'rgba(255, 71, 168, 0.7)',  // Pink
    'rgba(255, 107, 71, 0.7)',  // Orange
    'rgba(71, 255, 200, 0.7)',  // Teal
    'rgba(255, 215, 71, 0.7)',  // Gold
  ], []);

  // Optimized pseudo-random number generator for better performance
  const seededRandom = useMemo(() => {
    let seed = 1337; // Fixed seed for consistent generation
    return () => {
      seed = (seed * 9301 + 49297) % 233280;
      return seed / 233280;
    };
  }, []);

  // Optimized particle distribution for better visual coverage with fewer particles
  const distributeParticles = useCallback((count: number, margin: number = 0) => {
    const positions = [];
    const gridSize = Math.ceil(Math.sqrt(count));
    const cellSize = 100 / gridSize;

    for (let i = 0; i < count; i++) {
      const col = i % gridSize;
      const row = Math.floor(i / gridSize);

      // Add some randomness within each grid cell for natural distribution
      const x = (col * cellSize) + (seededRandom() * cellSize * 0.8) + (cellSize * 0.1) + margin;
      const y = (row * cellSize) + (seededRandom() * cellSize * 0.8) + (cellSize * 0.1) + margin;

      positions.push({ x: Math.min(x, 100 - margin), y: Math.min(y, 100 - margin) });
    }

    return positions;
  }, [seededRandom]);

  // Memoize particle generation with advanced optimization techniques
  const generatedParticles = useMemo(() => {
    if (!isLoaded) return { particles: [], shapes: [], dust: [] };

    // Generate nebula particles with optimized distribution
    const newParticles = [];
    if (ENABLE_NEBULA) {
      const positions = distributeParticles(PARTICLE_COUNT, 5);

      for (let i = 0; i < PARTICLE_COUNT; i++) {
        // Use optimized ranges for better visual impact with fewer particles
        newParticles.push({
          id: i,
          x: positions[i].x,
          y: positions[i].y,
          size: 10 + (seededRandom() * 15), // More consistent sizing
          opacity: 0.3 + (seededRandom() * 0.4), // More consistent opacity
          color: colors[Math.floor(seededRandom() * colors.length)],
          speed: 20 + (seededRandom() * 15), // More consistent animation speed
          delay: seededRandom() * 3, // Shorter delay range
          scale: 0.9 + (seededRandom() * 0.3), // More consistent scaling
          rotation: seededRandom() * 360
        });
      }
    }

    // Generate geometric shapes with optimized distribution
    const shapes = [];
    if (ENABLE_GEOMETRIC) {
      const shapeTypes = ['circle', 'square', 'triangle', 'hexagon'];
      const positions = distributeParticles(SHAPE_COUNT, 10);

      for (let i = 0; i < SHAPE_COUNT; i++) {
        shapes.push({
          id: i,
          x: positions[i].x,
          y: positions[i].y,
          size: 25 + (seededRandom() * 20), // More consistent sizing
          opacity: 0.08 + (seededRandom() * 0.08), // Lower opacity for better performance
          color: colors[Math.floor(seededRandom() * colors.length)],
          speed: 25 + (seededRandom() * 20), // More consistent animation speed
          delay: seededRandom() * 3, // Shorter delay range
          type: shapeTypes[Math.floor(seededRandom() * shapeTypes.length)] as 'circle' | 'square' | 'triangle' | 'hexagon',
          rotation: seededRandom() * 360
        });
      }
    }

    // Generate cosmic dust particles with optimized distribution
    const dust = [];
    if (ENABLE_DUST) {
      const positions = distributeParticles(DUST_COUNT, 0);

      for (let i = 0; i < DUST_COUNT; i++) {
        dust.push({
          id: i,
          x: positions[i].x,
          y: positions[i].y,
          size: 0.5 + (seededRandom() * 1.5), // Smaller size for better performance
          opacity: 0.3 + (seededRandom() * 0.4), // More consistent opacity
          color: 'rgba(255, 255, 255, 0.8)',
          speed: 6 + (seededRandom() * 6) // More consistent animation speed
        });
      }
    }

    return { particles: newParticles, shapes, dust };
  }, [isLoaded, colors, distributeParticles, seededRandom]);

  // Set state only once when the memoized values change
  useEffect(() => {
    if (!isLoaded) return;

    setParticles(generatedParticles.particles);
    setGeometricShapes(generatedParticles.shapes);
    setDustParticles(generatedParticles.dust);
  }, [isLoaded, generatedParticles]);

  // Optimized particle rendering with advanced memoization and type safety
  const GeometricShape = React.memo(({ shape }: {
    shape: {
      id: number;
      x: number;
      y: number;
      size: number;
      opacity: number;
      color: string;
      speed: number;
      delay: number;
      type: 'circle' | 'square' | 'triangle' | 'hexagon';
      rotation: number;
    }
  }) => {
    // Precomputed class mapping for better performance
    const shapeClassMap = useMemo(() => ({
      'circle': 'shape-circle',
      'square': 'shape-square',
      'triangle': 'shape-triangle',
      'hexagon': 'shape-hexagon'
    }), []);

    // Memoize styles for better performance
    const styles = useMemo(() => ({
      left: `${shape.x}%`,
      top: `${shape.y}%`,
      width: `${shape.size}px`,
      height: `${shape.size}px`,
      backgroundColor: shape.color,
      opacity: shape.opacity,
      animationDuration: `${shape.speed}s`,
      animationDelay: `${shape.delay}s`,
      transform: `rotate(${shape.rotation}deg)`,
      willChange: 'transform, opacity',
      // Add contain property for better performance
      contain: 'layout style paint',
      // Add pointer-events: none for better performance
      pointerEvents: 'none' as 'none'
    }), [shape]);

    return (
      <div
        key={shape.id}
        className={`geometric-shape ${shapeClassMap[shape.type]}`}
        style={styles}
      />
    );
  }, (prevProps, nextProps) => {
    // Custom equality check for better performance
    const prev = prevProps.shape;
    const next = nextProps.shape;
    return prev.id === next.id &&
           prev.x === next.x &&
           prev.y === next.y &&
           prev.opacity === next.opacity;
  });

  // Optimized nebula particle component with advanced memoization
  const NebulaParticle = React.memo(({ particle }: {
    particle: {
      id: number;
      x: number;
      y: number;
      size: number;
      opacity: number;
      color: string;
      speed: number;
      delay: number;
      scale: number;
      rotation: number;
    }
  }) => {
    // Memoize styles for better performance
    const styles = useMemo(() => ({
      left: `${particle.x}%`,
      top: `${particle.y}%`,
      width: `${particle.size}px`,
      height: `${particle.size}px`,
      backgroundColor: particle.color,
      opacity: particle.opacity,
      animationDuration: `${particle.speed}s`,
      animationDelay: `${particle.delay}s`,
      transform: `scale(${particle.scale}) rotate(${particle.rotation}deg)`,
      willChange: 'transform, opacity',
      // Add contain property for better performance
      contain: 'layout style paint',
      // Add pointer-events: none for better performance
      pointerEvents: 'none' as 'none'
    }), [particle]);

    return (
      <div
        key={particle.id}
        className="nebula-particle"
        style={styles}
      />
    );
  }, (prevProps, nextProps) => {
    // Custom equality check for better performance
    const prev = prevProps.particle;
    const next = nextProps.particle;
    return prev.id === next.id &&
           prev.x === next.x &&
           prev.y === next.y &&
           prev.opacity === next.opacity;
  });

  // Optimized dust particle component with advanced memoization
  const DustParticle = React.memo(({ dust }: {
    dust: {
      id: number;
      x: number;
      y: number;
      size: number;
      opacity: number;
      color: string;
      speed: number;
    }
  }) => {
    // Memoize styles for better performance
    const styles = useMemo(() => ({
      left: `${dust.x}%`,
      top: `${dust.y}%`,
      width: `${dust.size}px`,
      height: `${dust.size}px`,
      backgroundColor: dust.color,
      opacity: dust.opacity,
      animationDuration: `${dust.speed}s`,
      willChange: 'transform',
      // Add contain property for better performance
      contain: 'layout style paint',
      // Add pointer-events: none for better performance
      pointerEvents: 'none' as 'none'
    }), [dust]);

    return (
      <div
        key={dust.id}
        className="dust-particle"
        style={styles}
      />
    );
  }, (prevProps, nextProps) => {
    // Custom equality check for better performance
    const prev = prevProps.dust;
    const next = nextProps.dust;
    return prev.id === next.id &&
           prev.x === next.x &&
           prev.y === next.y &&
           prev.opacity === next.opacity;
  });

  // Advanced visibility detection with spatial partitioning for extreme performance
  const isParticleVisible = useCallback((x: number, y: number, size: number = 0) => {
    // Skip visibility check if component is not visible
    if (!isVisibleRef.current) return false;

    // Add a buffer zone around the viewport based on particle size
    const buffer = Math.max(20, size * 2);
    return x >= -buffer && x <= (100 + buffer) && y >= -buffer && y <= (100 + buffer);
  }, []);

  // Spatial partitioning for ultra-fast visibility checks
  const createSpatialIndex = useCallback((items: any[]) => {
    // Create a simple grid-based spatial index (4x4 grid)
    const grid: Record<string, any[]> = {};
    const gridSize = 4;

    items.forEach(item => {
      // Determine grid cell
      const cellX = Math.floor(item.x / (100 / gridSize));
      const cellY = Math.floor(item.y / (100 / gridSize));
      const cellKey = `${cellX},${cellY}`;

      // Add to grid
      if (!grid[cellKey]) grid[cellKey] = [];
      grid[cellKey].push(item);
    });

    return grid;
  }, []);

  // Get visible items from spatial index with frustum culling
  const getVisibleItems = useCallback((items: any[], spatialIndex: Record<string, any[]>) => {
    // If we have very few items, just check them all directly
    if (items.length <= 10) {
      return items.filter(item => isParticleVisible(item.x, item.y, item.size));
    }

    // Otherwise use spatial index for faster filtering
    const gridSize = 4;
    const cellWidth = 100 / gridSize;
    const cellHeight = 100 / gridSize;
    const visibleItems: any[] = [];

    // Determine visible grid cells (with buffer)
    const minCellX = Math.max(0, Math.floor(-20 / cellWidth));
    const maxCellX = Math.min(gridSize - 1, Math.floor(120 / cellWidth));
    const minCellY = Math.max(0, Math.floor(-20 / cellHeight));
    const maxCellY = Math.min(gridSize - 1, Math.floor(120 / cellHeight));

    // Collect items from visible cells
    for (let x = minCellX; x <= maxCellX; x++) {
      for (let y = minCellY; y <= maxCellY; y++) {
        const cellKey = `${x},${y}`;
        if (spatialIndex[cellKey]) {
          visibleItems.push(...spatialIndex[cellKey]);
        }
      }
    }

    return visibleItems;
  }, [isParticleVisible]);

  // Create spatial indices for all particle types
  const spatialIndices = useMemo(() => ({
    particles: createSpatialIndex(particles),
    shapes: createSpatialIndex(geometricShapes),
    dust: createSpatialIndex(dustParticles)
  }), [particles, geometricShapes, dustParticles, createSpatialIndex]);

  // Filter visible particles with advanced spatial partitioning
  const visibleParticles = useMemo(() =>
    ENABLE_NEBULA ? getVisibleItems(particles, spatialIndices.particles) : [],
  [particles, spatialIndices.particles, getVisibleItems, ENABLE_NEBULA]);

  const visibleShapes = useMemo(() =>
    ENABLE_GEOMETRIC ? getVisibleItems(geometricShapes, spatialIndices.shapes) : [],
  [geometricShapes, spatialIndices.shapes, getVisibleItems, ENABLE_GEOMETRIC]);

  const visibleDust = useMemo(() =>
    ENABLE_DUST ? getVisibleItems(dustParticles, spatialIndices.dust) : [],
  [dustParticles, spatialIndices.dust, getVisibleItems, ENABLE_DUST]);

  // Performance stats for monitoring
  const renderStats = useMemo(() => ({
    totalParticles: particles.length + geometricShapes.length + dustParticles.length,
    visibleParticles: visibleParticles.length + visibleShapes.length + visibleDust.length,
    cullingEfficiency: particles.length + geometricShapes.length + dustParticles.length > 0 ?
      (1 - ((visibleParticles.length + visibleShapes.length + visibleDust.length) /
            (particles.length + geometricShapes.length + dustParticles.length))) * 100 : 0
  }), [particles, geometricShapes, dustParticles, visibleParticles, visibleShapes, visibleDust]);

  // Log performance stats if monitoring is enabled
  useEffect(() => {
    if (ENABLE_PERFORMANCE_MONITORING) {
      console.log(`Rendering ${renderStats.visibleParticles}/${renderStats.totalParticles} particles (${renderStats.cullingEfficiency.toFixed(1)}% culled)`);
    }
  }, [renderStats, ENABLE_PERFORMANCE_MONITORING]);

  // Lazy-loaded components for better initial load performance
  const LazyAuroraEffect = useMemo(() => lazy(() => {
    // Simulate module loading with a small delay for better UX
    return new Promise<{ default: React.ComponentType }>(resolve => {
      setTimeout(() => {
        resolve({
          default: () => (
            <div className="aurora-container">
              <div className="aurora-layer aurora-1"></div>
              <div className="aurora-layer aurora-2"></div>
              <div className="aurora-layer aurora-3"></div>
            </div>
          )
        });
      }, 100);
    });
  }), []);

  // Optimized batch rendering for particles
  const renderParticles = useCallback(() => {
    // Skip rendering if not visible or disabled
    if (!isVisibleRef.current) return null;

    return (
      <>
        {/* Geometric shapes - using memoized components */}
        {ENABLE_GEOMETRIC && visibleShapes.length > 0 && (
          <div className="geometric-shapes-container parallax-layer" data-speed="0.8">
            {visibleShapes.map((shape) => (
              <GeometricShape key={shape.id} shape={shape} />
            ))}
          </div>
        )}

        {/* Enhanced nebula effect - using memoized components */}
        {ENABLE_NEBULA && visibleParticles.length > 0 && (
          <div className="nebula-container parallax-layer" data-speed="0.5">
            {visibleParticles.map((particle) => (
              <NebulaParticle key={particle.id} particle={particle} />
            ))}
          </div>
        )}

        {/* Cosmic dust particles - using memoized components */}
        {ENABLE_DUST && visibleDust.length > 0 && (
          <div className="cosmic-dust-container parallax-layer" data-speed="1.2">
            {visibleDust.map((dust) => (
              <DustParticle key={dust.id} dust={dust} />
            ))}
          </div>
        )}
      </>
    );
  }, [visibleShapes, visibleParticles, visibleDust, isVisibleRef, ENABLE_GEOMETRIC, ENABLE_NEBULA, ENABLE_DUST]);

  // Memoized wave effect component
  const WaveEffect = useMemo(() => {
    if (!ENABLE_WAVES) return null;
    return (
      <div className="wave-container">
        <div className="wave wave-1"></div>
        <div className="wave wave-2"></div>
        <div className="wave wave-3"></div>
      </div>
    );
  }, [ENABLE_WAVES]);

  // Memoized shooting stars component
  const ShootingStars = useMemo(() => {
    if (!ENABLE_SHOOTING_STARS) return null;
    return (
      <>
        <div className="shooting-star star-1"></div>
        <div className="shooting-star star-2"></div>
        <div className="shooting-star star-3"></div>
        <div className="shooting-star star-4"></div>
        <div className="shooting-star star-5"></div>
      </>
    );
  }, [ENABLE_SHOOTING_STARS]);

  // Optimized render method with conditional rendering and error boundaries
  return (
    <div className="space-background" ref={backgroundRef}>
      {/* Core background elements - always render these */}
      <div className="background-gradient"></div>

      {/* Stars background with parallax - essential for the look */}
      <div className="stars-background">
        <div className="stars-layer stars-small parallax-layer" data-speed="0.2"></div>
        <div className="stars-layer stars-medium parallax-layer" data-speed="0.4"></div>
        <div className="stars-layer stars-large parallax-layer" data-speed="0.6"></div>
        <div className="stars-layer stars-twinkle parallax-layer" data-speed="0.3"></div>
      </div>

      {/* Lazy-loaded aurora effect */}
      {ENABLE_AURORA && (
        <Suspense fallback={null}>
          <LazyAuroraEffect />
        </Suspense>
      )}

      {/* Optimized particle rendering */}
      {renderParticles()}

      {/* Optimized wave effect */}
      {WaveEffect}

      {/* Sunlight/glow effect - important for the look */}
      <div className="sunlight-effect top-left parallax-layer" data-speed="0.3"></div>
      <div className="sunlight-effect bottom-right parallax-layer" data-speed="0.3"></div>

      {/* Ambient color shift overlay - important for the look */}
      <div className="ambient-overlay"></div>

      {/* Optimized shooting stars */}
      {ShootingStars}

      {/* Performance monitoring display (only in dev mode) */}
      {ENABLE_PERFORMANCE_MONITORING && (
        <div className="performance-stats" style={{
          position: 'absolute',
          bottom: '10px',
          left: '10px',
          color: 'white',
          fontSize: '10px',
          opacity: 0.7,
          zIndex: 1000,
          pointerEvents: 'none'
        }}>
          Particles: {renderStats.visibleParticles}/{renderStats.totalParticles} ({renderStats.cullingEfficiency.toFixed(1)}% culled)
        </div>
      )}
    </div>
  );
};

// Export memoized component for better performance
export default React.memo(SpaceBackground);
