.space-logo-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0.8);
  opacity: 0;
  transition: transform 1s cubic-bezier(0.19, 1, 0.22, 1),
              opacity 1s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: transform, opacity;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: layout size style; /* Containment for better performance */
}

.space-logo-container.loaded {
  transform: scale(1);
  opacity: 1;
}

/* Enhanced outer glow effect */
.space-logo-glow {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(107, 71, 255, 0.7) 0%, rgba(107, 71, 255, 0.3) 40%, rgba(107, 71, 255, 0) 70%);
  filter: blur(20px);
  animation: enhancedLogoGlowPulse 6s ease-in-out infinite;
  z-index: 0;
  mix-blend-mode: screen;
  will-change: transform, opacity, filter;
}

/* Orbital rings */
.space-logo-orbital-ring {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 10px rgba(107, 71, 255, 0.5), inset 0 0 10px rgba(107, 71, 255, 0.5);
  will-change: transform;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: layout size style; /* Containment for better performance */
}

.ring-1 {
  width: 180px;
  height: 180px;
  animation: ringRotate1 20s linear infinite;
}

.ring-2 {
  width: 150px;
  height: 150px;
  animation: ringRotate2 15s linear infinite;
}

.ring-3 {
  width: 120px;
  height: 120px;
  animation: ringRotate3 10s linear infinite;
}

/* Main logo elements */
.space-logo-outer {
  position: relative;
  width: 90px;
  height: 90px;
  border-radius: 18px;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.5), inset 0 0 20px rgba(107, 71, 255, 0.2);
  animation: enhancedLogoRotate 30s linear infinite;
  will-change: transform;
  backdrop-filter: blur(5px);
}

.space-logo-inner {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, #6b47ff, #8a6fff, #6b47ff);
  background-size: 200% 200%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: enhancedLogoInnerPulse 6s ease-in-out infinite,
             gradientFlow 10s linear infinite;
  will-change: transform, background-position;
  box-shadow: 0 0 15px rgba(107, 71, 255, 0.5);
}

.space-logo-core {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  animation: enhancedLogoCoreScale 3s ease-in-out infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  will-change: transform;
}

/* 37 Text */
.logo-text {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 20px;
  color: #6b47ff;
  text-shadow: 0 0 5px rgba(107, 71, 255, 0.5);
  animation: textPulse 3s ease-in-out infinite;
  will-change: transform, opacity;
}

/* Enhanced particles */
.space-logo-particles {
  position: absolute;
  width: 200px;
  height: 200px;
  z-index: 1;
  animation: particleContainerRotate 30s linear infinite;
  will-change: transform;
}

.space-logo-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  filter: blur(1px);
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
  will-change: transform, opacity;
}

.particle-1 {
  top: 10%;
  left: 50%;
  animation: enhancedParticleFloat 8s ease-in-out infinite;
  width: 5px;
  height: 5px;
}

.particle-2 {
  top: 50%;
  left: 10%;
  animation: enhancedParticleFloat 9s ease-in-out infinite 0.5s;
  width: 4px;
  height: 4px;
}

.particle-3 {
  top: 90%;
  left: 50%;
  animation: enhancedParticleFloat 7s ease-in-out infinite 1s;
  width: 6px;
  height: 6px;
}

.particle-4 {
  top: 50%;
  left: 90%;
  animation: enhancedParticleFloat 10s ease-in-out infinite 1.5s;
  width: 3px;
  height: 3px;
}

.particle-5 {
  top: 20%;
  left: 80%;
  animation: enhancedParticleFloat 11s ease-in-out infinite 2s;
  width: 5px;
  height: 5px;
}

.particle-6 {
  top: 80%;
  left: 20%;
  animation: enhancedParticleFloat 9s ease-in-out infinite 2.5s;
  width: 4px;
  height: 4px;
}

.particle-7 {
  top: 30%;
  left: 70%;
  animation: enhancedParticleFloat 8s ease-in-out infinite 3s;
  width: 5px;
  height: 5px;
}

.particle-8 {
  top: 70%;
  left: 30%;
  animation: enhancedParticleFloat 10s ease-in-out infinite 3.5s;
  width: 4px;
  height: 4px;
}

/* Energy beams */
.space-logo-beams {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.beam {
  position: absolute;
  width: 2px;
  height: 40px;
  background: linear-gradient(to top, rgba(107, 71, 255, 0), rgba(107, 71, 255, 0.8), rgba(255, 255, 255, 0.9), rgba(107, 71, 255, 0.8), rgba(107, 71, 255, 0));
  filter: blur(1px);
  transform-origin: center center;
  opacity: 0;
  will-change: transform, opacity;
}

.beam-1 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(0deg);
  animation: beamPulse 4s ease-in-out infinite;
}

.beam-2 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
  animation: beamPulse 4s ease-in-out infinite 1s;
}

.beam-3 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  animation: beamPulse 4s ease-in-out infinite 2s;
}

.beam-4 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(135deg);
  animation: beamPulse 4s ease-in-out infinite 3s;
}

/* Enhanced animations */
@keyframes enhancedLogoGlowPulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1) rotate(0deg);
    filter: blur(20px) brightness(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.2) rotate(180deg);
    filter: blur(25px) brightness(1.3);
  }
}

@keyframes ringRotate1 {
  0% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, 360deg);
  }
}

@keyframes ringRotate2 {
  0% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, -360deg);
  }
}

@keyframes ringRotate3 {
  0% {
    transform: rotate3d(0, 0, 1, 0deg) scale3d(1, 1, 1);
  }
  50% {
    transform: rotate3d(0, 0, 1, 180deg) scale3d(1.05, 1.05, 1);
  }
  100% {
    transform: rotate3d(0, 0, 1, 360deg) scale3d(1, 1, 1);
  }
}

@keyframes enhancedLogoRotate {
  0% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
  25% {
    transform: rotate3d(0, 0, 1, 90deg);
  }
  50% {
    transform: rotate3d(0, 0, 1, 180deg);
  }
  75% {
    transform: rotate3d(0, 0, 1, 270deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, 360deg);
  }
}

@keyframes enhancedLogoInnerPulse {
  0%, 100% {
    transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, 0deg);
  }
  50% {
    transform: scale3d(0.95, 0.95, 1) rotate3d(0, 0, 1, 180deg);
  }
}

@keyframes enhancedLogoCoreScale {
  0%, 100% {
    transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, 0deg);
  }
  50% {
    transform: scale3d(0.9, 0.9, 1) rotate3d(0, 0, 1, -90deg);
  }
}

@keyframes textPulse {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
    text-shadow: 0 0 5px rgba(107, 71, 255, 0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
    text-shadow: 0 0 10px rgba(107, 71, 255, 0.8);
  }
}

@keyframes particleContainerRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

@keyframes enhancedParticleFloat {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    opacity: 0.8;
    filter: blur(1px) brightness(1);
  }
  25% {
    transform: translate3d(15px, -15px, 0) scale3d(1.2, 1.2, 1);
    opacity: 1;
    filter: blur(2px) brightness(1.5);
  }
  50% {
    transform: translate3d(0, -30px, 0) scale3d(1.5, 1.5, 1);
    opacity: 0.6;
    filter: blur(1px) brightness(1);
  }
  75% {
    transform: translate3d(-15px, -15px, 0) scale3d(1.2, 1.2, 1);
    opacity: 1;
    filter: blur(2px) brightness(1.5);
  }
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes beamPulse {
  0%, 100% {
    height: 0;
    opacity: 0;
    transform: translate3d(0, 0, 0);
  }
  20% {
    height: 60px;
    opacity: 0.8;
    transform: translate3d(0, 0, 0);
  }
  40% {
    height: 80px;
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  60% {
    height: 100px;
    opacity: 0.8;
    transform: translate3d(0, 0, 0);
  }
  80%, 100% {
    height: 0;
    opacity: 0;
    transform: translate3d(0, 0, 0);
  }
}