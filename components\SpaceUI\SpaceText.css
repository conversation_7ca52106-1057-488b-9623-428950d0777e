/* Enhanced text animations */
.space-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.space-greeting {
  font-size: 1.5rem;
  font-weight: 400;
  color: #EDEDED;
  margin: 0 0 0.5rem 0;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  opacity: 0;
  animation: textFadeIn 0.8s ease-out forwards 0.6s;
}

.space-greeting::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  transform: translateX(-100%);
  animation: textShimmer 3s infinite 1.5s;
}

.space-question {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  background: linear-gradient(90deg, #6b47ff, #a28bff, #6b47ff);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textFadeIn 0.8s ease-out forwards 1s,
             gradientFlow 8s linear infinite 2s;
  opacity: 0;
}

.space-description {
  font-size: 1rem;
  font-weight: 300;
  line-height: 1.6;
  color: #BBBBBB;
  max-width: 600px;
  margin: 0;
  letter-spacing: 0.3px;
  opacity: 0;
  transform: translateY(20px);
  animation: textReveal 0.8s ease-out forwards 1.4s;
}

.space-description span {
  display: inline-block;
  opacity: 0;
  transform: translateY(10px);
  animation: wordAppear 0.5s ease-out forwards;
}

.space-text-word {
  display: inline-block;
  opacity: 0;
  transform: translateY(10px);
  animation: wordAppear 0.5s ease-out forwards;
}

@keyframes textFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textShimmer {
  0% {
    transform: translateX(-100%);
  }
  20%, 100% {
    transform: translateX(100%);
  }
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes wordAppear {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .space-greeting {
    font-size: 1.3rem;
  }

  .space-question {
    font-size: 2rem;
  }

  .space-description {
    font-size: 0.9rem;
    max-width: 90%;
  }
}

@media (max-width: 480px) {
  .space-greeting {
    font-size: 1.2rem;
  }

  .space-question {
    font-size: 1.7rem;
  }

  .space-description {
    font-size: 0.85rem;
    max-width: 100%;
  }
}