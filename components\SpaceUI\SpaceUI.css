/* Main container for the space UI */
.space-ui-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  background: #0f0f23; /* Darker blue background for better contrast with effects */
  color: #FFFFFF;
  font-family: 'Space Mono', monospace, 'Inter', sans-serif;
  text-align: center;
  padding: 2rem;
  overflow: hidden; /* Prevent any potential overflow */
  position: relative;
  transition: opacity 0.8s ease-in-out;
  opacity: 0;
  perspective: 1000px; /* For 3D effects */
}

.space-ui-container.loaded {
  opacity: 1;
}

/* Stars background */
.stars-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.stars-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: repeat;
  opacity: 0.6;
}

.stars-small {
  background-image: radial-gradient(1px 1px at 50px 200px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 150px 150px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 250px 400px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 350px 250px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 450px 350px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 550px 200px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 650px 300px, #ffffff, rgba(0, 0, 0, 0));
  background-size: 700px 700px;
  animation: starsAnimation 200s linear infinite;
}

.stars-medium {
  background-image: radial-gradient(1.5px 1.5px at 100px 300px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 200px 250px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 300px 350px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 400px 150px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 500px 400px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 600px 200px, #ffffff, rgba(0, 0, 0, 0));
  background-size: 600px 600px;
  animation: starsAnimation 150s linear infinite;
}

.stars-large {
  background-image: radial-gradient(2px 2px at 150px 150px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(2px 2px at 250px 250px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(2px 2px at 350px 350px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(2px 2px at 450px 450px, #ffffff, rgba(0, 0, 0, 0));
  background-size: 500px 500px;
  animation: starsAnimation 100s linear infinite;
}

@keyframes starsAnimation {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-700px);
  }
}

/* Content container */
.space-ui-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10; /* Higher z-index to appear above background effects */
  max-width: 800px;
  margin: 0 auto;
  animation: contentFloat 8s ease-in-out infinite;
}

@keyframes contentFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Chat input container */
.space-chat-input-container {
  position: fixed;
  bottom: 80px; /* Position above the switch button */
  left: 0;
  right: 0;
  z-index: 100;
  width: 100%;
  animation: fadeInUp 1.2s cubic-bezier(0.19, 1, 0.22, 1) forwards;
  animation-delay: 1.8s;
  opacity: 0;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.3));
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add hover effect to the chat input container */
.space-chat-input-container:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* Switch button */
.switch-button-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
}

.switch-button {
  background-color: #4a3b8b;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.switch-button:hover {
  background-color: #5a4b9b;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .space-ui-greeting {
    font-size: 1.3rem;
  }

  .space-ui-question {
    font-size: 2rem;
  }

  .space-ui-description {
    font-size: 0.9rem;
    max-width: 90%;
  }
}

@media (max-width: 480px) {
  .space-ui-container {
    padding: 1.5rem;
  }

  .space-ui-greeting {
    font-size: 1.2rem;
  }

  .space-ui-question {
    font-size: 1.7rem;
  }

  .space-ui-description {
    font-size: 0.85rem;
    max-width: 100%;
  }

  .space-ui-logo {
    width: 60px;
    height: 60px;
    margin-bottom: 1.5rem;
  }

  .logo-inner-shape {
    width: 30px;
    height: 30px;
  }
}