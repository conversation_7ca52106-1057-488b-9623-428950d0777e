import React, { useState, useEffect } from 'react';
import './SpaceCursor.css';

const SpaceCursor: React.FC = () => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isPointer, setIsPointer] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [isMoving, setIsMoving] = useState(false);
  const [movingTimeout, setMovingTimeout] = useState<number | undefined>(undefined);

  useEffect(() => {
    // Show cursor when mouse moves
    const addIsVisible = () => {
      if (!isVisible) setIsVisible(true);
    };
    window.addEventListener('mousemove', addIsVisible);

    // Track cursor position
    const updatePosition = (e: MouseEvent) => {
      setPosition({ x: e.clientX, y: e.clientY });
      
      // Set moving state
      setIsMoving(true);
      clearTimeout(movingTimeout);
      const newTimeout = window.setTimeout(() => setIsMoving(false), 100);
      setMovingTimeout(newTimeout);
    };
    window.addEventListener('mousemove', updatePosition);

    // Track cursor style changes on an interval
    const cursorInterval = setInterval(() => {
        const el = document.elementFromPoint(position.x, position.y);
        const pointer = el && window.getComputedStyle(el).getPropertyValue('cursor') === 'pointer';
        setIsPointer(!!pointer);
    }, 100);

    // Track mouse down/up events
    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);

    // Hide cursor when mouse leaves window
    const handleMouseLeave = () => setIsVisible(false);
    document.documentElement.addEventListener('mouseleave', handleMouseLeave);
    document.documentElement.addEventListener('mouseenter', addIsVisible);

    // Cleanup
    return () => {
      window.removeEventListener('mousemove', updatePosition);
      window.removeEventListener('mousemove', addIsVisible);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
      document.documentElement.removeEventListener('mouseleave', handleMouseLeave);
      document.documentElement.removeEventListener('mouseenter', addIsVisible);
      clearInterval(cursorInterval);
      clearTimeout(movingTimeout);
    };
  }, [position.x, position.y, isVisible]); // Removed movingTimeout from dependencies

  if (!isVisible) return null;

  return (
    <>
      <div 
        className={`space-cursor-outer ${isPointer ? 'pointer' : ''} ${isClicking ? 'clicking' : ''} ${isMoving ? 'moving' : ''}`}
        style={{ 
          left: `${position.x}px`, 
          top: `${position.y}px` 
        }}
      />
      <div 
        className={`space-cursor-inner ${isPointer ? 'pointer' : ''} ${isClicking ? 'clicking' : ''}`}
        style={{ 
          left: `${position.x}px`, 
          top: `${position.y}px` 
        }}
      />
      <div 
        className="space-cursor-glow"
        style={{ 
          left: `${position.x}px`, 
          top: `${position.y}px` 
        }}
      />
    </>
  );
};

export default SpaceCursor;