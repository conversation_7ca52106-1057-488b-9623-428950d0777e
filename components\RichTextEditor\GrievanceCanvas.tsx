import React, { useState, useEffect, useCallback, useRef } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { UserProfile, Theme } from "../../types"; // Assuming types.ts is in ../../types
import { DocumentDuplicateIcon } from "../icons"; // Assuming icons are here (index.tsx)

interface GrievanceCanvasProps {
  value: string;
  onChange: (markdown: string) => void;
  isGenerating: boolean;
  onUpdateDraft: () => void; // Function to ask LLM to update draft
  onSubmit: () => void; // Function to open submission modal
  theme: Theme;
  userProfile: UserProfile;
  onUpdateSection?: (
    sectionTitle: string,
    content: string,
    action: "append" | "replace"
  ) => void;
}

const GrievanceCanvas: React.FC<GrievanceCanvasProps> = ({
  value,
  onChange,
  isGenerating,
  onUpdateDraft,
  onSubmit,
  theme,
  userProfile,
  onUpdateSection,
}) => {
  const editorRef = useRef<HTMLTextAreaElement>(null);
  const [showCopyMessage, setShowCopyMessage] = useState(false);
  const [viewMode, setViewMode] = useState<"split" | "edit" | "preview">(
    "split"
  );
  const [isEditorFocused, setIsEditorFocused] = useState(false);

  // Auto-resize textarea
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.style.height = "auto";
      editorRef.current.style.height = editorRef.current.scrollHeight + "px";
    }
  }, [value]);

  const handleCopyClick = useCallback(() => {
    if (editorRef.current) {
      // Replace placeholders before copying
      const formattedContent = getPreviewMarkdown(value, userProfile);
      navigator.clipboard
        .writeText(formattedContent)
        .then(() => {
          setShowCopyMessage(true);
          setTimeout(() => setShowCopyMessage(false), 2000); // Hide message after 2 seconds
        })
        .catch((err) => {
          console.error("Failed to copy text:", err);
          // Fallback for non-secure contexts or older browsers
          // document.execCommand('copy') is generally preferred in Canvas if clipboard.writeText fails due to iframe restrictions.
          const tempInput = document.createElement("textarea");
          tempInput.value = formattedContent;
          document.body.appendChild(tempInput);
          tempInput.select();
          document.execCommand("copy");
          document.body.removeChild(tempInput);
          setShowCopyMessage(true); // Still show message for fallback
          setTimeout(() => setShowCopyMessage(false), 2000);
        });
    }
  }, [value, userProfile]);

  // UpdateTool functionality for inline markdown modifications
  const insertMarkdownSection = useCallback(
    (
      markdown: string,
      sectionTitle: string,
      updateContent: string,
      action: "append" | "replace"
    ): string => {
      const sectionRegex = new RegExp(
        `(#{1,6}\\s*${sectionTitle}.*?)(?=#{1,6}|$)`,
        "gis"
      );
      const match = markdown.match(sectionRegex);

      if (match) {
        if (action === "replace") {
          return markdown.replace(
            sectionRegex,
            `## ${sectionTitle}\n\n${updateContent}\n\n`
          );
        } else {
          return markdown.replace(
            sectionRegex,
            `${match[0]}\n\n${updateContent}\n\n`
          );
        }
      } else {
        // Section doesn't exist, append at the end
        return `${markdown}\n\n## ${sectionTitle}\n\n${updateContent}\n\n`;
      }
    },
    []
  );

  const getPreviewMarkdown = useCallback(
    (markdown: string, profile: UserProfile): string => {
      if (!markdown) return "";
      let formatted = markdown;
      // Basic replacement of common placeholders
      formatted = formatted.replace(
        /\[Your Full Name\]/gi,
        `${profile.firstName} ${profile.lastName}`
      );
      formatted = formatted.replace(/\[Your Email Address\]/gi, profile.email);
      formatted = formatted.replace(
        /\[Your Contact Number\]/gi,
        `(*************`
      ); // mock phone
      formatted = formatted.replace(
        /\[Appropriate Department, e.g., Human Resources\]/gi,
        "Human Resources Department"
      );
      formatted = formatted.replace(
        /\[Your Department, if applicable\]/gi,
        "N/A"
      );
      formatted = formatted.replace(
        /\[Briefly state the core issue, e.g., Workplace Harassment\]/gi,
        "Unspecified Grievance"
      );
      formatted = formatted.replace(
        /\[List names and titles, or "Unknown" if not applicable\]/gi,
        "To be determined"
      );
      formatted = formatted.replace(
        /\[Date or Date Range\]/gi,
        "Date/Range Missing"
      );
      formatted = formatted.replace(
        /\[Specific location, e.g., Office, Floor, Online\]/gi,
        "Location Missing"
      );
      formatted = formatted.replace(
        /\[Provide a clear, chronological, and factual account of the events\. Use bullet points for multiple events if it improves clarity\.\]/gi,
        "Detailed description missing."
      );
      formatted = formatted.replace(
        /\[Clearly state the outcome or action you are seeking to resolve this grievance\.\]/gi,
        "Desired resolution missing."
      );
      return formatted;
    },
    []
  );

  let inputClasses,
    previewClasses,
    buttonClasses,
    copyButtonClasses,
    updateButtonClasses,
    submitButtonClasses,
    headingColor;

  switch (theme) {
    case Theme.Light:
      inputClasses =
        "bg-white border-gray-300 text-gray-800 focus:ring-blue-500";
      previewClasses = "bg-gray-50 border-gray-200 text-black";
      buttonClasses = "bg-gray-200 hover:bg-gray-300 text-gray-800";
      copyButtonClasses = "bg-blue-500 hover:bg-blue-600 text-white";
      updateButtonClasses = "bg-blue-500 hover:bg-blue-600 text-white";
      submitButtonClasses = "bg-blue-600 hover:bg-blue-700 text-white";
      headingColor = "text-gray-800";
      break;
    default: // Dark and Space themes
      inputClasses =
        "bg-gray-800 border-gray-700 text-white focus:ring-purple-500";
      previewClasses = "bg-gray-900/50 border-gray-700 text-gray-200";
      buttonClasses = "bg-gray-700 hover:bg-gray-600 text-white";
      copyButtonClasses = "bg-purple-600 hover:bg-purple-500 text-white";
      updateButtonClasses = "bg-purple-600 hover:bg-purple-500 text-white";
      submitButtonClasses = "bg-purple-700 hover:bg-purple-800 text-white";
      headingColor = "text-white";
      break;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Toggle Buttons */}
      <div className="flex gap-2 justify-end items-center p-4 border-b border-gray-600">
        <button
          onClick={() => setViewMode("edit")}
          className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
            viewMode === "edit"
              ? "bg-blue-500 text-white"
              : theme === Theme.Light
              ? "bg-gray-200 text-gray-700 hover:bg-gray-300"
              : "bg-gray-700 text-gray-300 hover:bg-gray-600"
          }`}
        >
          ✏️ Edit
        </button>
        <button
          onClick={() => setViewMode("split")}
          className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
            viewMode === "split"
              ? "bg-blue-500 text-white"
              : theme === Theme.Light
              ? "bg-gray-200 text-gray-700 hover:bg-gray-300"
              : "bg-gray-700 text-gray-300 hover:bg-gray-600"
          }`}
        >
          📄 Split
        </button>
        <button
          onClick={() => setViewMode("preview")}
          className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
            viewMode === "preview"
              ? "bg-blue-500 text-white"
              : theme === Theme.Light
              ? "bg-gray-200 text-gray-700 hover:bg-gray-300"
              : "bg-gray-700 text-gray-300 hover:bg-gray-600"
          }`}
        >
          👁️ Preview
        </button>
      </div>

      <div
        className={`flex-grow flex gap-4 p-4 ${
          viewMode === "split" ? "flex-col md:flex-row" : "flex-col"
        }`}
      >
        {/* Editor Section */}
        {(viewMode === "edit" || viewMode === "split") && (
          <div
            className={`${
              viewMode === "split" ? "flex-1" : "w-full"
            } flex flex-col p-4 rounded-lg border shadow-inner ${inputClasses}`}
          >
            <h3 className={`text-lg font-semibold mb-3 ${headingColor}`}>
              Edit Draft
            </h3>
            <textarea
              ref={editorRef}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="Your grievance report will be drafted here..."
              className={`flex-grow resize-none overflow-y-auto outline-none ${inputClasses.replace(
                "focus:ring-2 focus:ring-blue-500",
                "focus:ring-0"
              )}`} // Remove outer ring, inner element provides focus ring
              disabled={isGenerating}
              rows={10} // Initial rows
            ></textarea>
            <div className="flex justify-between items-center mt-3">
              <button
                onClick={onUpdateDraft}
                disabled={isGenerating}
                className={`px-4 py-2 rounded-lg font-semibold flex items-center transition-all duration-200 ${updateButtonClasses} ${
                  isGenerating
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:scale-[1.02]"
                }`}
              >
                {isGenerating ? (
                  <>
                    <span className="animate-spin inline-block mr-2">⚙️</span>{" "}
                    Updating...
                  </>
                ) : (
                  "Update Draft"
                )}
              </button>
              <div className="relative">
                <button
                  onClick={handleCopyClick}
                  className={`px-4 py-2 rounded-lg font-semibold flex items-center transition-all duration-200 ${copyButtonClasses} ${
                    isGenerating
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:scale-[1.02]"
                  }`}
                  disabled={isGenerating || !value.trim()}
                >
                  <DocumentDuplicateIcon className="w-5 h-5 mr-2" /> Copy to
                  Clipboard
                </button>
                {showCopyMessage && (
                  <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-green-500 text-white text-xs px-2 py-1 rounded-md animate-fade-in-out whitespace-nowrap">
                    Copied!
                  </span>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Preview Section */}
        {(viewMode === "preview" || viewMode === "split") && (
          <div
            className={`${
              viewMode === "split" ? "flex-1" : "w-full"
            } flex flex-col p-4 rounded-lg border shadow-inner overflow-hidden ${previewClasses}`}
          >
            <h3 className={`text-lg font-semibold mb-3 ${headingColor}`}>
              Preview
            </h3>
            <div
              className={`flex-grow overflow-y-auto p-4 rounded-md prose max-w-none ${
                theme === Theme.Light
                  ? "prose-gray bg-gray-50 border border-gray-200"
                  : "prose-invert bg-gray-900 border border-gray-700"
              }`}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {getPreviewMarkdown(value, userProfile)}
              </ReactMarkdown>
            </div>
          </div>
        )}
      </div>
      {/* Submit Button */}
      <div className="p-4 border-t border-gray-700 flex justify-end">
        <button
          onClick={onSubmit}
          disabled={isGenerating || !value.trim()}
          className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${submitButtonClasses} ${
            isGenerating || !value.trim()
              ? "opacity-50 cursor-not-allowed"
              : "hover:scale-105"
          }`}
        >
          Submit Grievance
        </button>
      </div>
    </div>
  );
};

export { GrievanceCanvas };
