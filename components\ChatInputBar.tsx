import React, { useState, useRef } from "react";
import { SendIcon, MicrophoneIcon, PaperClipIcon } from "./icons"; // Ensure MicrophoneIcon and PaperClipIcon are in your icons file
import { Theme } from "../types";

interface ChatInputBarProps {
  onSubmit: (message: string) => void;
  placeholder?: string;
  disclaimer?: string;
  theme: Theme;
  // New props for multimodal input
  onFileSelect?: (file: File) => void;
  onAudioRecordStart?: () => void;
  onAudioRecordStop?: () => void;
  isRecording?: boolean;
  isDisabled?: boolean; // To disable the input bar entirely, e.g., during LLM response
}

const ChatInputBar: React.FC<ChatInputBarProps> = ({
  onSubmit,
  placeholder = "Type your message...",
  disclaimer,
  theme,
  onFileSelect,
  onAudioRecordStart,
  onAudioRecordStop,
  isRecording = false,
  isDisabled = false,
}) => {
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSend = () => {
    if (inputValue.trim()) {
      console.log("🔧 ChatInputBar sending message:", inputValue.trim());
      onSubmit(inputValue.trim());
      setInputValue("");
      setIsTyping(false);
      inputRef.current?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    setIsTyping(e.target.value.length > 0);
  };

  const handleFileButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onFileSelect?.(e.target.files[0]);
      e.target.value = ""; // Clear the input so the same file can be selected again
    }
  };

  const handleMicClick = () => {
    if (isRecording) {
      onAudioRecordStop?.();
    } else {
      onAudioRecordStart?.();
    }
  };

  let containerClasses,
    inputClasses,
    buttonClasses,
    disclaimerClasses,
    iconButtonClasses;
  let baseIconButtonClasses =
    "p-2.5 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed";

  switch (theme) {
    case Theme.Light:
      containerClasses =
        "relative bg-white border border-gray-300 rounded-xl shadow-sm";
      // Adjusted padding-left to make space for icons
      inputClasses =
        "w-full pl-[5.5rem] pr-16 py-4 bg-transparent text-gray-800 placeholder-gray-500 text-base focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-xl";
      buttonClasses =
        "absolute right-3 top-1/2 -translate-y-1/2 p-2.5 rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-all";
      disclaimerClasses = "text-center text-xs text-gray-500 mt-2";
      iconButtonClasses = `text-gray-500 hover:bg-gray-100 focus:ring-blue-500 ${baseIconButtonClasses}`;
      break;
    default: // Dark and Space themes
      containerClasses =
        "relative bg-black/60 backdrop-blur-md border border-purple-800/30 rounded-full shadow-2xl shadow-purple-500/10";
      // Adjusted padding-left to make space for icons
      inputClasses =
        "w-full pl-[5.5rem] pr-16 py-4 bg-transparent text-white placeholder-gray-400 text-base focus:outline-none";
      buttonClasses =
        "absolute right-1.5 top-1/2 -translate-y-1/2 p-3.5 rounded-full bg-purple-600 text-white hover:bg-purple-500 transition-all";
      disclaimerClasses = "text-center text-xs text-gray-400 mt-3";
      iconButtonClasses = `text-gray-400 hover:bg-white/10 focus:ring-purple-500 ${baseIconButtonClasses}`;
      break;
  }

  return (
    <div className="w-full max-w-4xl mx-auto px-4">
      <div className={containerClasses}>
        <div className="absolute left-3 top-1/2 -translate-y-1/2 flex space-x-1">
          {onAudioRecordStart && (
            <button
              onClick={handleMicClick}
              className={`${iconButtonClasses} ${
                isRecording ? "text-red-500 animate-pulse" : ""
              }`}
              aria-label={isRecording ? "Stop recording" : "Start recording"}
              disabled={isDisabled}
            >
              <MicrophoneIcon className="w-5 h-5" />
            </button>
          )}
          {onFileSelect && (
            <>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                disabled={isDisabled}
              />
              <button
                onClick={handleFileButtonClick}
                className={iconButtonClasses}
                aria-label="Attach file"
                disabled={isDisabled}
              >
                <PaperClipIcon className="w-5 h-5" />
              </button>
            </>
          )}
        </div>

        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`${inputClasses} transition-all duration-300 ${
            isTyping ? "ring-2 ring-blue-500/50" : ""
          }`}
          disabled={isDisabled || isRecording}
        />
        <button
          onClick={handleSend}
          className={buttonClasses}
          disabled={isDisabled || !inputValue.trim() || isRecording}
        >
          <SendIcon className="w-5 h-5" />
        </button>
      </div>
      {disclaimer && <p className={disclaimerClasses}>{disclaimer}</p>}
    </div>
  );
};

export default ChatInputBar;
