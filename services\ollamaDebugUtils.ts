// services/ollamaDebugUtils.ts

/**
 * Runs a basic debug suite to check Ollama model availability.
 * @param modelName The name of the Ollama model to check.
 */
export const runOllamaDebugSuite = async (modelName: string) => {
    console.log(`🔧 Running Ollama debug suite for model: ${modelName}`);
    try {
        const response = await fetch('http://localhost:11434/api/show', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: modelName })
        });

        if (response.ok) {
            console.log(`✅ Ollama model '${modelName}' is available.`);
        } else {
            const errorText = await response.text();
            console.error(`❌ Ollama model '${modelName}' check failed:`, response.status, errorText);
            console.warn(`💡 Please ensure 'ollama serve' is running and you have pulled the model with 'ollama pull ${modelName}'.`);
        }
    } catch (error) {
        console.error('❌ Failed to connect to Ollama server. Is it running?', error);
        console.warn('💡 Make sure Ollama is running: `ollama serve`');
    }
};

/**
 * Logs the current Ollama configuration (if any specific config is being used).
 * This is a placeholder for more advanced configuration logging.
 */
export const logOllamaConfig = () => {
    console.log("🔧 Ollama Config: Basic logging initialized. (No specific custom config detected for logging)");
    // In a more complex setup, you might log environment variables or other settings here.
};
