// Speech recognition utilities for voice input

export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
}

export interface SpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
}

// Check if speech recognition is supported
export const isSpeechRecognitionSupported = (): boolean => {
  return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
};

// Create a speech recognition instance
export const createSpeechRecognition = (
  options: SpeechRecognitionOptions = {}
): SpeechRecognition | null => {
  if (!isSpeechRecognitionSupported()) {
    console.warn('Speech recognition is not supported in this browser');
    return null;
  }

  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
  const recognition = new SpeechRecognition();

  // Configure recognition
  recognition.lang = options.language || 'en-US';
  recognition.continuous = options.continuous || false;
  recognition.interimResults = options.interimResults || false;
  recognition.maxAlternatives = 1;

  return recognition;
};

// Start voice recording with transcription
export const startVoiceRecording = (
  onResult: (result: SpeechRecognitionResult) => void,
  onError: (error: string) => void,
  options: SpeechRecognitionOptions = {}
): SpeechRecognition | null => {
  const recognition = createSpeechRecognition(options);
  
  if (!recognition) {
    onError('Speech recognition is not supported in this browser');
    return null;
  }

  recognition.onresult = (event) => {
    const result = event.results[event.results.length - 1];
    const transcript = result[0].transcript;
    const confidence = result[0].confidence;
    
    console.log('🎙️ Voice transcription:', transcript, 'Confidence:', confidence);
    onResult({ transcript, confidence });
  };

  recognition.onerror = (event) => {
    console.error('Speech recognition error:', event.error);
    onError(`Speech recognition error: ${event.error}`);
  };

  recognition.onend = () => {
    console.log('🎙️ Speech recognition ended');
  };

  try {
    recognition.start();
    console.log('🎙️ Speech recognition started');
    return recognition;
  } catch (error) {
    console.error('Failed to start speech recognition:', error);
    onError('Failed to start speech recognition');
    return null;
  }
};

// Stop voice recording
export const stopVoiceRecording = (recognition: SpeechRecognition | null): void => {
  if (recognition) {
    recognition.stop();
    console.log('🎙️ Speech recognition stopped');
  }
};

// Convert audio blob to text (placeholder for future STT integration)
export const transcribeAudioBlob = async (audioBlob: Blob): Promise<string> => {
  // This is a placeholder for future integration with STT services
  // For now, we'll use the Web Speech API for real-time transcription
  console.log('🎙️ Audio blob transcription not yet implemented, using Web Speech API instead');
  return '';
};

// Declare global types for TypeScript
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}
