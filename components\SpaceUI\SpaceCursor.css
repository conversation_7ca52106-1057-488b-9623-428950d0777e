.space-ui-container {
  cursor: none; /* Hide default cursor */
}

.space-cursor-outer {
  position: fixed;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 9999;
  transition: width 0.3s ease, height 0.3s ease, border-color 0.3s ease;
  mix-blend-mode: difference;
}

.space-cursor-inner {
  position: fixed;
  width: 8px;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10000;
  transition: width 0.2s ease, height 0.2s ease, background-color 0.2s ease;
}

.space-cursor-glow {
  position: fixed;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(107, 71, 255, 0.2) 0%, rgba(107, 71, 255, 0) 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 9998;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

/* Pointer state */
.space-cursor-outer.pointer {
  width: 50px;
  height: 50px;
  border-color: rgba(107, 71, 255, 0.7);
}

.space-cursor-inner.pointer {
  width: 10px;
  height: 10px;
  background-color: rgba(107, 71, 255, 0.9);
}

/* Clicking state */
.space-cursor-outer.clicking {
  width: 35px;
  height: 35px;
  border-color: rgba(255, 255, 255, 0.8);
  transition: all 0.1s ease;
}

.space-cursor-inner.clicking {
  width: 12px;
  height: 12px;
  background-color: #ffffff;
  transition: all 0.1s ease;
}

/* Moving state */
.space-cursor-outer.moving {
  transition: all 0.1s linear;
}

/* Media query to disable custom cursor on touch devices */
@media (hover: none) and (pointer: coarse) {
  .space-cursor-outer,
  .space-cursor-inner,
  .space-cursor-glow {
    display: none;
  }
  
  .space-ui-container {
    cursor: auto; /* Restore default cursor */
  }
}