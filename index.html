<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Grievance Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Space+Mono:wght@400;700&display=swap');
       body {
         font-family: 'Inter', sans-serif;
       }
    </style>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            typography: (theme) => ({
              DEFAULT: {
                css: {
                  '--tw-prose-body': theme('colors.gray[700]'),
                  '--tw-prose-headings': theme('colors.gray[900]'),
                  '--tw-prose-bold': theme('colors.gray[900]'),
                   '--tw-prose-invert-body': theme('colors.gray[300]'),
                  '--tw-prose-invert-headings': theme('colors.white'),
                  '--tw-prose-invert-bold': theme('colors.white'),
                },
              },
            }),
            colors: {
                'brand-purple': '#6b47ff',
                'glass-border': 'rgba(255, 255, 255, 0.1)',
                'glass-bkg': 'rgba(255, 255, 255, 0.05)',
            },
            fontFamily: {
                sans: ['Inter', 'sans-serif'],
                mono: ['Space Mono', 'monospace'],
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0', transform: 'translateY(10px)' },
                '100%': { opacity: '1', transform: 'translateY(0)' },
              },
              slideInUp: {
                '0%': { opacity: '0', transform: 'translateY(20px) scale(0.98)' },
                '100%': { opacity: '1', transform: 'translateY(0) scale(1)' },
              },
              pulse: {
                '0%, 100%': { opacity: '1' },
                '50%': { opacity: '.5' },
              },
              auroraGlow: {
                '0%': { opacity: '0.4', filter: 'blur(60px) brightness(1)' },
                '50%': { opacity: '0.6', filter: 'blur(60px) brightness(1.3)' },
                '100%': { opacity: '0.4', filter: 'blur(60px) brightness(1)' },
              },
              glow: {
                '0%, 100%': { 'text-shadow': '0 0 5px rgba(189, 147, 249, 0.3), 0 0 10px rgba(189, 147, 249, 0.3)', color: '#f3e8ff' },
                '50%': { 'text-shadow': '0 0 15px rgba(189, 147, 249, 0.7), 0 0 30px rgba(189, 147, 249, 0.7)', color: '#fff' },
              }
            },
            animation: {
              fadeIn: 'fadeIn 0.5s ease-out forwards',
              slideInUp: 'slideInUp 0.5s ease-out forwards',
              pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              glow: 'glow 3s ease-in-out infinite',
            },
            backdropBlur: {
              'xl': '24px',
            }
          }
        },
        plugins: [
          require('@tailwindcss/typography'),
        ],
      }
    </script>

<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>