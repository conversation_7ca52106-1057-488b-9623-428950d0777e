/* Main container with extreme performance optimizations */
.space-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  background: linear-gradient(125deg, #0f0f23 0%, #1a1a2e 25%, #2c1e4a 50%, #1e1a3a 75%, #0f0f23 100%);
  animation: gradientShift 30s ease infinite;
  will-change: background-position;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden; /* Prevent flickering */
  contain: layout size style paint; /* Containment for better performance */
  isolation: isolate; /* Create stacking context for better performance */
  -webkit-font-smoothing: antialiased; /* Smoother text rendering */
  -moz-osx-font-smoothing: grayscale; /* Smoother text rendering */
}

/* Premium pulsating background gradient - optimized for performance */
.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(107, 71, 255, 0.05) 0%, rgba(0, 0, 0, 0) 70%);
  z-index: 1;
  animation: pulseGradient 15s ease-in-out infinite;
  will-change: transform, opacity;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: paint; /* Containment for better performance */
  pointer-events: none; /* Improve performance by disabling pointer events */
}

/* Optimized keyframe animations with reduced steps and hardware acceleration */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes pulseGradient {
  0% {
    opacity: 0.5;
    transform: scale3d(1, 1, 1);
  }
  50% {
    opacity: 0.8;
    transform: scale3d(1.2, 1.2, 1);
  }
  100% {
    opacity: 0.5;
    transform: scale3d(1, 1, 1);
  }
}

/* Enhanced stars background - extreme performance optimization */
.stars-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  overflow: hidden;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: layout size style paint; /* Containment for better performance */
  pointer-events: none; /* Improve performance by disabling pointer events */
}

.stars-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: repeat;
  opacity: 0.8;
  transition: transform 0.2s cubic-bezier(0.215, 0.61, 0.355, 1); /* Optimized easing */
  will-change: transform;
  contain: paint; /* Containment for better performance */
  image-rendering: optimizeSpeed; /* Optimize rendering for speed */
}

.stars-small {
  background-image: radial-gradient(1px 1px at 50px 200px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 150px 150px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 250px 400px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 350px 250px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 450px 350px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 550px 200px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 650px 300px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 750px 250px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 850px 150px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 950px 350px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 1050px 250px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 1150px 350px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 1250px 450px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 1350px 550px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(1px 1px at 1450px 650px, #ffffff, rgba(0, 0, 0, 0));
  background-size: 1500px 1500px;
  animation: starsAnimation 300s linear infinite;
}

.stars-medium {
  background-image: radial-gradient(1.5px 1.5px at 100px 300px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 200px 250px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 300px 350px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 400px 150px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 500px 400px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 600px 200px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 700px 300px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 800px 200px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 900px 300px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 1000px 400px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(1.5px 1.5px at 1100px 500px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0));
  background-size: 1500px 1500px;
  animation: starsAnimation 200s linear infinite;
}

.stars-large {
  background-image: radial-gradient(2.5px 2.5px at 150px 150px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(2.5px 2.5px at 250px 250px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(2.5px 2.5px at 350px 350px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(2.5px 2.5px at 450px 450px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(2.5px 2.5px at 550px 550px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(2.5px 2.5px at 650px 650px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(2.5px 2.5px at 750px 750px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(2.5px 2.5px at 850px 850px, #ffffff, rgba(0, 0, 0, 0));
  background-size: 1500px 1500px;
  animation: starsAnimation 150s linear infinite;
}

/* Twinkling stars with glow */
.stars-twinkle {
  background-image: radial-gradient(3px 3px at 120px 120px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(3px 3px at 220px 420px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(3px 3px at 320px 320px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(3px 3px at 420px 220px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(3px 3px at 520px 520px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(3px 3px at 620px 120px, #ffffff, rgba(0, 0, 0, 0)),
                    radial-gradient(3px 3px at 720px 320px, rgba(255, 255, 255, 0.9), rgba(0, 0, 0, 0)),
                    radial-gradient(3px 3px at 820px 420px, rgba(255, 255, 255, 0.8), rgba(0, 0, 0, 0)),
                    radial-gradient(3px 3px at 920px 520px, #ffffff, rgba(0, 0, 0, 0));
  background-size: 1500px 1500px;
  filter: blur(1px);
  animation: twinkleAnimation 10s ease-in-out infinite;
}

/* Optimized star animations for better performance */
@keyframes starsAnimation {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(0, -1500px, 0);
  }
}

@keyframes twinkleAnimation {
  0% {
    opacity: 0.3;
    filter: blur(1px) brightness(1);
  }
  50% {
    opacity: 0.9;
    filter: blur(1.5px) brightness(1.2);
  }
  100% {
    opacity: 0.3;
    filter: blur(1px) brightness(1);
  }
}

/* Aurora/Northern lights effect - optimized for performance */
.aurora-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  overflow: hidden;
  opacity: 0.4;
  mix-blend-mode: screen;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: layout size style paint; /* Containment for better performance */
  pointer-events: none; /* Improve performance by disabling pointer events */
}

.aurora-layer {
  position: absolute;
  width: 200%;
  height: 100%;
  filter: blur(60px);
  border-radius: 50%;
  transform-origin: center center;
  will-change: transform, opacity, filter;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: paint; /* Containment for better performance */
  image-rendering: optimizeSpeed; /* Optimize rendering for speed */
}

.aurora-1 {
  top: 60%;
  left: -50%;
  height: 80%;
  background: linear-gradient(90deg,
    rgba(107, 71, 255, 0),
    rgba(107, 71, 255, 0.5),
    rgba(71, 125, 255, 0.5),
    rgba(71, 255, 200, 0.5),
    rgba(71, 255, 200, 0));
  animation: auroraWave 20s ease-in-out infinite alternate,
             auroraGlow 8s ease-in-out infinite;
}

.aurora-2 {
  top: 40%;
  left: -30%;
  height: 60%;
  background: linear-gradient(90deg,
    rgba(255, 71, 168, 0),
    rgba(255, 71, 168, 0.5),
    rgba(255, 107, 71, 0.5),
    rgba(255, 215, 71, 0.5),
    rgba(255, 215, 71, 0));
  animation: auroraWave 15s ease-in-out infinite alternate-reverse,
             auroraGlow 10s ease-in-out infinite 2s;
}

.aurora-3 {
  top: 20%;
  left: -40%;
  height: 40%;
  background: linear-gradient(90deg,
    rgba(71, 255, 200, 0),
    rgba(71, 255, 200, 0.5),
    rgba(107, 71, 255, 0.5),
    rgba(71, 125, 255, 0.5),
    rgba(71, 125, 255, 0));
  animation: auroraWave 25s ease-in-out infinite alternate,
             auroraGlow 12s ease-in-out infinite 4s;
}

/* Optimized aurora animations for better performance */
@keyframes auroraWave {
  0% {
    transform: translate3d(0, 0, 0) scaleY(1);
  }
  50% {
    transform: translate3d(25%, 0, 0) scaleY(1.2);
  }
  100% {
    transform: translate3d(50%, 0, 0) scaleY(1);
  }
}

@keyframes auroraGlow {
  0% {
    opacity: 0.4;
    filter: blur(60px) brightness(1);
  }
  50% {
    opacity: 0.6;
    filter: blur(60px) brightness(1.3);
  }
  100% {
    opacity: 0.4;
    filter: blur(60px) brightness(1);
  }
}

/* Geometric shapes */
.geometric-shapes-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
  overflow: hidden;
  opacity: 0.5;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: layout size style; /* Containment for better performance */
}

.geometric-shape {
  position: absolute;
  filter: blur(3px);
  mix-blend-mode: screen;
  animation: shapeFloat 20s ease-in-out infinite;
  will-change: transform;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.shape-circle {
  border-radius: 50%;
}

.shape-square {
  border-radius: 4px;
}

.shape-triangle {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.shape-hexagon {
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}

@keyframes shapeFloat {
  0%, 100% {
    transform: translate3d(0, 0, 0) rotate(0deg) scale(1);
  }
  25% {
    transform: translate3d(20px, -15px, 0) rotate(5deg) scale(1.05);
  }
  50% {
    transform: translate3d(40px, 0, 0) rotate(10deg) scale(1.1);
  }
  75% {
    transform: translate3d(20px, 15px, 0) rotate(5deg) scale(1.05);
  }
}

/* Enhanced Nebula effect */
.nebula-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  overflow: hidden;
  mix-blend-mode: screen;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: layout size style; /* Containment for better performance */
}

.nebula-particle {
  position: absolute;
  border-radius: 50%;
  filter: blur(15px);
  animation: nebulaFloat 30s ease-in-out infinite;
  mix-blend-mode: screen;
  will-change: transform, opacity;
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
}

@keyframes nebulaFloat {
  0%, 100% {
    transform: translate3d(0, 0, 0) rotate(0deg) scale(1);
    filter: blur(15px) brightness(1);
  }
  25% {
    transform: translate3d(30px, -20px, 0) rotate(15deg) scale(1.1);
    filter: blur(18px) brightness(1.2);
  }
  50% {
    transform: translate3d(60px, 0, 0) rotate(30deg) scale(1.2);
    filter: blur(20px) brightness(1.3);
  }
  75% {
    transform: translate3d(30px, 20px, 0) rotate(15deg) scale(1.1);
    filter: blur(18px) brightness(1.2);
  }
}

/* Cosmic dust particles */
.cosmic-dust-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 6;
  overflow: hidden;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: layout size style; /* Containment for better performance */
}

.dust-particle {
  position: absolute;
  border-radius: 50%;
  animation: dustFloat 15s linear infinite;
  will-change: transform;
}

@keyframes dustFloat {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 0.3;
  }
  25% {
    opacity: 0.8;
  }
  50% {
    transform: translate3d(20px, -30px, 0);
    opacity: 0.5;
  }
  75% {
    opacity: 0.8;
  }
  100% {
    transform: translate3d(0, -60px, 0);
    opacity: 0.3;
  }
}

/* Wave effect */
.wave-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 7;
  overflow: hidden;
  opacity: 0.15;
  mix-blend-mode: screen;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: layout size style; /* Containment for better performance */
}

.wave {
  position: absolute;
  width: 200%;
  height: 150px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  border-radius: 50%;
  left: -50%;
  transform-origin: center bottom;
}

.wave-1 {
  bottom: -100px;
  height: 200px;
  animation: waveAnimation 15s ease-in-out infinite alternate;
}

.wave-2 {
  bottom: -120px;
  height: 250px;
  animation: waveAnimation 18s ease-in-out infinite alternate-reverse;
  opacity: 0.7;
}

.wave-3 {
  bottom: -150px;
  height: 300px;
  animation: waveAnimation 20s ease-in-out infinite alternate;
  opacity: 0.5;
}

@keyframes waveAnimation {
  0% {
    transform: scale3d(1, 0.3, 1) translate3d(-30%, 0, 0) rotate(0deg);
  }
  50% {
    transform: scale3d(1.2, 0.4, 1) translate3d(0%, 0, 0) rotate(0.5deg);
  }
  100% {
    transform: scale3d(1, 0.3, 1) translate3d(30%, 0, 0) rotate(0deg);
  }
}

/* Enhanced Sunlight/glow effects */
.sunlight-effect {
  position: absolute;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.25;
  z-index: 8;
  animation: enhancedPulseGlow 15s ease-in-out infinite;
  mix-blend-mode: screen;
  will-change: transform, opacity, filter;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: paint; /* Containment for better performance */
}

.top-left {
  top: -300px;
  left: -300px;
  background: radial-gradient(circle, rgba(255, 107, 71, 0.9) 0%, rgba(255, 107, 71, 0.5) 30%, rgba(255, 107, 71, 0) 70%);
  transform-origin: top left;
}

.bottom-right {
  bottom: -300px;
  right: -300px;
  background: radial-gradient(circle, rgba(107, 71, 255, 0.9) 0%, rgba(107, 71, 255, 0.5) 30%, rgba(107, 71, 255, 0) 70%);
  animation-delay: 7.5s;
  transform-origin: bottom right;
}

@keyframes enhancedPulseGlow {
  0%, 100% {
    opacity: 0.25;
    transform: scale3d(1, 1, 1) rotate(0deg);
    filter: blur(100px) brightness(1);
  }
  50% {
    opacity: 0.35;
    transform: scale3d(1.2, 1.2, 1) rotate(5deg);
    filter: blur(120px) brightness(1.2);
  }
}

/* Enhanced Ambient color shift overlay */
.ambient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(107, 71, 255, 0.08), rgba(255, 107, 71, 0.08));
  z-index: 9;
  mix-blend-mode: overlay;
  animation: enhancedAmbientShift 30s ease-in-out infinite;
  will-change: background, background-position;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: paint; /* Containment for better performance */
}

@keyframes enhancedAmbientShift {
  0%, 100% {
    background: linear-gradient(135deg, rgba(107, 71, 255, 0.08), rgba(255, 107, 71, 0.08));
    background-size: 200% 200%;
    background-position: 0% 0%;
  }
  25% {
    background: linear-gradient(135deg, rgba(71, 255, 200, 0.08), rgba(107, 71, 255, 0.08));
    background-size: 200% 200%;
    background-position: 50% 50%;
  }
  50% {
    background: linear-gradient(135deg, rgba(255, 107, 71, 0.08), rgba(71, 255, 200, 0.08));
    background-size: 200% 200%;
    background-position: 100% 100%;
  }
  75% {
    background: linear-gradient(135deg, rgba(255, 215, 71, 0.08), rgba(255, 71, 168, 0.08));
    background-size: 200% 200%;
    background-position: 50% 50%;
  }
}

/* Enhanced Shooting stars */
.shooting-star {
  position: absolute;
  width: 200px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 40%, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  z-index: 10;
  transform: rotate(-45deg);
  animation: enhancedShootingStar 15s linear infinite;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  filter: blur(0.5px);
  will-change: transform, opacity, filter;
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden;
  contain: paint; /* Containment for better performance */
}

.star-1 {
  top: 15%;
  left: 80%;
  animation-delay: 0s;
  width: 250px;
}

.star-2 {
  top: 30%;
  left: 40%;
  animation-delay: 4s;
  transform: rotate(-35deg);
}

.star-3 {
  top: 60%;
  left: 70%;
  animation-delay: 7s;
  transform: rotate(-55deg);
  width: 180px;
}

.star-4 {
  top: 45%;
  left: 85%;
  animation-delay: 11s;
  transform: rotate(-40deg);
  width: 220px;
}

.star-5 {
  top: 75%;
  left: 30%;
  animation-delay: 14s;
  transform: rotate(-50deg);
  width: 300px;
}

@keyframes enhancedShootingStar {
  0% {
    opacity: 0;
    transform: rotate(-45deg) translate3d(0, 0, 0) scale3d(1, 1, 1);
    filter: blur(0.5px) brightness(1);
  }
  0.5% {
    opacity: 0.3;
  }
  1% {
    opacity: 1;
    filter: blur(1px) brightness(1.5);
  }
  3% {
    opacity: 1;
    transform: rotate(-45deg) translate3d(-300px, 300px, 0) scale3d(0.8, 0.8, 1);
    filter: blur(1.5px) brightness(1.2);
  }
  5% {
    opacity: 0.3;
    transform: rotate(-45deg) translate3d(-600px, 600px, 0) scale3d(0.6, 0.6, 1);
    filter: blur(0.5px) brightness(1);
  }
  6%, 100% {
    opacity: 0;
    transform: rotate(-45deg) translate3d(-700px, 700px, 0) scale3d(0.5, 0.5, 1);
  }
}