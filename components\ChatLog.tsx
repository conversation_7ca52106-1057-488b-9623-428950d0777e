import React, { useEffect, useRef } from "react";
import { Message, Role, Theme } from "../types";

interface ChatLogProps {
  messages: Message[];
  isLoading?: boolean;
  theme: Theme;
  chatEndRef?: React.RefObject<HTMLDivElement>;
}

const ChatLog: React.FC<ChatLogProps> = ({
  messages,
  isLoading = false,
  theme,
  chatEndRef,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatEndRef?.current) {
      chatEndRef.current.scrollIntoView({ behavior: "smooth" });
    } else if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isLoading, chatEndRef]);

  // Theme-based styling
  const getMessageClasses = (role: Role) => {
    const baseClasses =
      "max-w-xs lg:max-w-md px-4 py-3 rounded-lg shadow-md transition-all duration-300 animate-slideInUp";

    if (role === Role.User) {
      switch (theme) {
        case Theme.Light:
          return `${baseClasses} bg-blue-500 text-white rounded-br-sm ml-auto`;
        case Theme.Dark:
          return `${baseClasses} bg-blue-600 text-white rounded-br-sm ml-auto`;
        case Theme.Space:
          return `${baseClasses} bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-br-sm ml-auto backdrop-blur-sm`;
        default:
          return `${baseClasses} bg-blue-500 text-white rounded-br-sm ml-auto`;
      }
    } else {
      switch (theme) {
        case Theme.Light:
          return `${baseClasses} bg-gray-100 text-gray-800 rounded-bl-sm mr-auto border border-gray-200`;
        case Theme.Dark:
          return `${baseClasses} bg-gray-700 text-white rounded-bl-sm mr-auto border border-gray-600`;
        case Theme.Space:
          return `${baseClasses} bg-black/20 text-white rounded-bl-sm mr-auto border border-white/20 backdrop-blur-md`;
        default:
          return `${baseClasses} bg-gray-200 text-gray-800 rounded-bl-sm mr-auto`;
      }
    }
  };

  const LoadingDots = () => (
    <div className="flex space-x-1">
      <span className="w-2 h-2 bg-current animate-pulse rounded-full delay-[0s]"></span>
      <span className="w-2 h-2 bg-current animate-pulse rounded-full delay-[200ms]"></span>
      <span className="w-2 h-2 bg-current animate-pulse rounded-full delay-[400ms]"></span>
    </div>
  );

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.map((message, index) => (
        <div
          key={message.id || index}
          className={`flex ${
            message.role === Role.User ? "justify-end" : "justify-start"
          } animate-fadeIn`}
          style={{
            animationDelay: `${index * 100}ms`,
            animationFillMode: "both",
          }}
        >
          <div className={getMessageClasses(message.role)}>
            {/* Message Content */}
            <div className="whitespace-pre-wrap break-words">
              {message.text}
            </div>

            {/* Display uploaded image if it's a user message with image data */}
            {message.role === Role.User && message.imageData && (
              <img
                src={message.imageData}
                alt="Uploaded content"
                className="mt-2 max-w-full h-auto rounded-lg shadow-md animate-fadeIn"
                style={{ maxWidth: "200px" }}
              />
            )}

            {/* Message timestamp */}
            <div
              className={`text-xs mt-1 opacity-70 ${
                message.role === Role.User ? "text-right" : "text-left"
              }`}
            >
              {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>
      ))}

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex justify-start animate-slideInUp">
          <div className={getMessageClasses(Role.Model)}>
            <div className="flex items-center space-x-2">
              <LoadingDots />
              <span className="text-sm opacity-80">AI is thinking...</span>
            </div>
          </div>
        </div>
      )}

      {/* Scroll anchor */}
      <div ref={chatEndRef || messagesEndRef} />
    </div>
  );
};

export default ChatLog;
