import React from 'react';

// Common icon properties for easier management
interface IconProps extends React.SVGProps<SVGSVGElement> {
    className?: string;
}

export const SendIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A5.996 5.996 0 0112 3a5.996 5.996 0 018.73 9.874L18 12m0 0l3.269 8.874A5.996 5.996 0 0112 21a5.996 5.996 0 01-8.73-9.874L6 12z" />
    </svg>
);

export const HomeIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
    </svg>
);

export const ChatBubbleLeftRightIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.515 20.25 15.485C20.25 16.592 19.342 17.498 18.235 17.498H5.765C4.658 17.498 3.75 16.592 3.75 15.485V8.515C3.75 7.408 4.658 6.502 5.765 6.502H18.235C19.342 6.502 20.25 7.408 20.25 8.515ZM10.5 12H13.5M7.5 12H9M15 12H16.5" />
    </svg>
);


export const SunIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16.5 12a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0z" />
    </svg>
);

export const MoonIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 12C21.75 16.2011 18.2011 19.75 14 19.75C9.7989 19.75 6.25 16.2011 6.25 12C6.25 7.79893 9.7989 4.25 14 4.25C18.2011 4.25 21.75 7.79893 21.75 12Z" />
    </svg>
);

export const StarIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.123 5.179 5.518.442c.407.032.584.559.27.876l-4.248 3.23.153 5.617c.017.414-.32.747-.73.61l-4.994-3.136a.562.562 0 01-.63 0l-4.994 3.136c-.41.137-.747-.196-.73-.61l.153-5.617-4.248-3.23c-.314-.317-.137-.844.27-.876l5.518-.442 2.123-5.179z" />
    </svg>
);

export const ChevronDoubleLeftIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 9l-3 3m0 0l3 3m-3-3h7.5M21 12a9 9 100 01-18 0" />
    </svg>
);

export const MicrophoneIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 18.75a6 6 0 006-6v-1.5m-6 6v-1.5m0 1.5a6 6 0 01-6-6v-1.5m6 6v-1.5m6-1.5a6 6 0 00-6-6h-.75V8.25a6 6 0 00-6-6h-.75V.75a.75.75 0 00-1.5 0v1.5H6.75a6 6 0 00-6 6v1.5a.75.75 0 000 1.5h1.5A.75.75 0 001.5 12v-.75a.75.75 0 00-.75-.75H.75v-.75a.75.75 0 00-1.5 0v.75a.75.75 0 00-.75.75H.75v1.5a.75.75 0 00.75.75H1.5v1.5a.75.75 0 00.75.75h.75v.75a.75.75 0 001.5 0v-.75H5.25a6 6 0 006 6v1.5a.75.75 0 001.5 0v-1.5H18.75a6 6 0 006 6v-1.5a.75.75 0 00-.75-.75H23.25a.75.75 0 00-.75-.75h-.75v-.75a.75.75 0 00-1.5 0v.75a.75.75 0 00-.75.75H20.25a6 6 0 00-6-6h-.75V8.25a6 6 0 00-6-6H6.75a6 6 0 00-6 6v1.5M12 18.75a.75.75 0 00-1.5 0v.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75v-.75z" />
    </svg>
);


export const PaperClipIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.182 18.657a.75.75 0 01-1.06 0l-5.18-5.18a.75.75 0 010-1.06l1.06-1.061A.75.75 0 0110.5 11.25h1.5a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0v-1.5h-.75l-2.06 2.06a.75.75 0 000 1.06l5.18 5.18a.75.75 0 001.06 0L17.25 19.34a.75.75 0 000-1.06l-2.06-2.06z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M12.75 12H15a.75.75 0 00.75-.75V9.75a.75.75 0 00-.75-.75h-1.5a.75.75 0 00-.75.75V11.25" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M8.89 4.394a.75.75 001-1.06 0l5.18 5.18a.75.75 000 1.06l-1.06 1.061A.75.75 000 12.75h-1.5a.75.75 00-.75-.75V9.75a.75.75 001.5 0v1.5h.75l2.06-2.06a.75.75 000-1.06l-5.18-5.18a.75.75 00-1.06 0L6.75 4.66a.75.75 000 1.06l2.06 2.06z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 12H9a.75.75 0 01-.75-.75V9.75a.75.75 0 01.75-.75h1.5a.75.75 0 01.75.75V11.25" />
    </svg>
);

export const DocumentDuplicateIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 17.25H12V14.25H15.75V17.25ZM9.75 17.25H6V14.25H9.75V17.25ZM15.75 11.25H12V8.25H15.75V11.25ZM9.75 11.25H6V8.25H9.75V11.25ZM15.75 5.25H12V2.25H15.75V5.25ZM9.75 5.25H6V2.25H9.75V5.25Z" />
    </svg>
);

export const ExclamationTriangleIcon: React.FC<IconProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.385 3.374 2.15 3.374h14.56c1.765 0 3.015-1.874 2.15-3.374L12.75 3.844c-.866-1.5-3.134-1.5-4 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
    </svg>
);
