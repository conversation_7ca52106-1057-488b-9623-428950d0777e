# Ollama API Debugging Guide for CivicAssist

This guide helps you troubleshoot common Ollama API integration issues in the CivicAssist project.

## Quick Debug Commands

### 1. Browser Console Debugging

Open your browser's developer console and run:

```javascript
// Test with default model (gemma3n:e4b)
window.debugOllama();

// Test with a specific model
window.debugOllama("llama3");
```

### 2. Check Ollama Server Status

```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama server if not running
ollama serve
```

### 3. Verify Available Models

```bash
# List all downloaded models
ollama list

# Pull a model if needed
ollama pull gemma3n:e4b
ollama pull llama3
```

## Common Issues & Solutions

### ❌ 404 Error: Model Not Found

**Problem**: `Ollama API error: 404 - Model 'model-name' not found`

**Solutions**:

1. Check available models: `ollama list`
2. Ensure exact model name match (case-sensitive)
3. Pull the model: `ollama pull gemma3n:e4b`
4. Update model name in `services/localLLMService.tsx` if needed

### ❌ Connection Refused

**Problem**: `Cannot connect to Ollama server`

**Solutions**:

1. Start Ollama server: `ollama serve`
2. Check if port 11434 is available
3. Verify base URL in code: `http://localhost:11434`

### ❌ 500 Server Error

**Problem**: `Ollama API error: 500 - Server error`

**Solutions**:

1. Restart Ollama server: `ollama serve`
2. Check Ollama logs for detailed error messages
3. Ensure sufficient system resources (RAM/GPU)

## Debug Features in Code

### Enhanced Error Logging

The project now includes detailed error logging that shows:

- Request details (URL, method, body)
- Response status and error data
- Specific troubleshooting steps

### Debug Utilities

Located in `services/ollamaDebugUtils.ts`:

- `testOllamaConnection()` - Test server connectivity
- `testModelAvailability()` - Check if model exists
- `testChatAPI()` - Test basic chat functionality
- `runOllamaDebugSuite()` - Run all tests

## Configuration

### Current Model Configuration

The project is configured to use: `gemma3n:e4b`

To change the model:

1. Update the model name in `services/localLLMService.tsx`
2. Ensure the new model is pulled: `ollama pull <new-model-name>`
3. Test with: `window.debugOllama('<new-model-name>')`

### API Endpoints Used

- Chat: `http://localhost:11434/api/chat`
- Generate: `http://localhost:11434/api/generate`
- Models: `http://localhost:11434/api/tags`

## Testing Checklist

Before reporting issues, verify:

- [ ] Ollama server is running (`ollama serve`)
- [ ] Model is available (`ollama list`)
- [ ] Basic connectivity works (`curl http://localhost:11434/api/tags`)
- [ ] Browser console shows no CORS errors
- [ ] Model name matches exactly (case-sensitive)
- [ ] Sufficient system resources available

## Manual Testing with cURL

Test the API directly:

```bash
# Test server connectivity
curl http://localhost:11434/api/tags

# Test chat API
curl http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemma3n:e4b",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

## Getting Help

If issues persist:

1. Run the debug suite: `window.debugOllama()`
2. Check browser console for detailed error logs
3. Verify Ollama server logs
4. Ensure model compatibility with your system

## Model Recommendations

For best performance:

- **Small/Fast**: `gemma3n:e4b` (4B parameters)
- **Balanced**: `llama3` (8B parameters)
- **Large/Accurate**: `llama3:70b` (requires significant resources)

Choose based on your system capabilities and performance requirements.
