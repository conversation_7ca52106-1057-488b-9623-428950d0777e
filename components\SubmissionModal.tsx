import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Theme, UserProfile } from "../types";
import { XMarkIcon, DocumentArrowDownIcon, PaperAirplaneIcon } from "./icons";

interface SubmissionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  reportContent: string;
  userProfile: UserProfile;
  theme: Theme;
}

const SubmissionModal: React.FC<SubmissionModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  reportContent,
  userProfile,
  theme,
}) => {
  if (!isOpen) return null;

  // Format the final document with user details
  const finalDocument = reportContent
    .replace(
      /\[Your Full Name\]/g,
      `${userProfile.firstName} ${userProfile.lastName}`
    )
    .replace(/\[Your Email\]/g, userProfile.email)
    .replace(/\[Your Phone\]/g, "(*************") // Mock phone number
    .replace(
      /\[Date\]/g,
      new Date().toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    );

  // Theme-based styling
  const getModalClasses = () => {
    switch (theme) {
      case Theme.Light:
        return "bg-white border border-gray-200 shadow-2xl";
      case Theme.Dark:
        return "bg-gray-800 border border-gray-700 shadow-2xl";
      case Theme.Space:
        return "bg-black/30 backdrop-blur-xl border border-white/20 shadow-2xl space-glow";
      default:
        return "bg-white border border-gray-200 shadow-2xl";
    }
  };

  const getTextClasses = () => {
    switch (theme) {
      case Theme.Light:
        return "text-gray-900";
      case Theme.Dark:
        return "text-white";
      case Theme.Space:
        return "text-white";
      default:
        return "text-gray-900";
    }
  };

  const getButtonClasses = (variant: "primary" | "secondary" | "danger") => {
    const baseClasses =
      "px-6 py-3 rounded-lg font-medium transition-all duration-300 hover-lift";

    switch (variant) {
      case "primary":
        switch (theme) {
          case Theme.Light:
            return `${baseClasses} bg-blue-500 text-white hover:bg-blue-600 shadow-lg`;
          case Theme.Dark:
            return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700 shadow-lg`;
          case Theme.Space:
            return `${baseClasses} bg-gradient-to-r from-purple-600 to-blue-500 text-white hover:from-purple-700 hover:to-blue-600 shadow-lg space-glow`;
        }
        break;
      case "secondary":
        switch (theme) {
          case Theme.Light:
            return `${baseClasses} bg-gray-200 text-gray-800 hover:bg-gray-300`;
          case Theme.Dark:
            return `${baseClasses} bg-gray-700 text-white hover:bg-gray-600`;
          case Theme.Space:
            return `${baseClasses} bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm`;
        }
        break;
      case "danger":
        return `${baseClasses} bg-red-500 text-white hover:bg-red-600 shadow-lg`;
    }
    return baseClasses;
  };

  const handleDownload = () => {
    const element = document.createElement("a");
    const file = new Blob([finalDocument], { type: "text/markdown" });
    element.href = URL.createObjectURL(file);
    element.download = `grievance-report-${
      new Date().toISOString().split("T")[0]
    }.md`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fadeIn">
      <div
        className={`max-w-4xl w-full max-h-[90vh] rounded-xl overflow-hidden animate-fadeInScale ${getModalClasses()}`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-600">
          <h3 className={`text-2xl font-bold ${getTextClasses()}`}>
            📋 Final Grievance Report
          </h3>
          <button
            onClick={onClose}
            className={`p-2 rounded-lg transition-colors ${
              theme === Theme.Light
                ? "text-gray-500 hover:bg-gray-100"
                : theme === Theme.Dark
                ? "text-gray-400 hover:bg-gray-700"
                : "text-white/70 hover:bg-white/10"
            }`}
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 custom-scrollbar">
          <div
            className={`prose max-w-none ${
              theme === Theme.Light ? "prose-gray" : "prose-invert"
            }`}
          >
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {finalDocument}
            </ReactMarkdown>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-600 bg-gradient-to-r from-transparent to-transparent">
          <div className="flex space-x-3">
            <button
              onClick={handleDownload}
              className={getButtonClasses("secondary")}
            >
              <DocumentArrowDownIcon className="w-5 h-5 inline mr-2" />
              Download
            </button>
          </div>

          <div className="flex space-x-3">
            <button onClick={onClose} className={getButtonClasses("secondary")}>
              Cancel
            </button>
            <button onClick={onConfirm} className={getButtonClasses("primary")}>
              <PaperAirplaneIcon className="w-5 h-5 inline mr-2" />
              Submit Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubmissionModal;
