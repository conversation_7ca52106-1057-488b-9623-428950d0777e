import { Message, Role, UserProfile } from "../types";
import { runOllamaDebugSuite, logOllamaConfig } from "./ollamaDebugUtils"; // New import for debugging utilities

// Define the base URL for your local LLM server (e.g., Ollama's default)
const OLLAMA_API_BASE_URL = "http://localhost:11434";

// A simple in-memory chat history to maintain conversation context for the local LLM.
// For a more robust application, this might be managed in a global state or a database.
let chatHistory: { role: string; content?: string; images?: string[] }[] = [];
let userProfileCache: UserProfile | null = null; // Cache user profile to reconstruct system instruction

// Define a simple tool simulation structure.
const LOCAL_TOOL_CALL_PREFIX = "CALL_TOOL:";
const TOOL_START_GRIEVANCE_REPORT = "startGrievanceReport";
const TOOL_UPDATE_REPORT = "updateReport";

// Debug: Log the expected tool call string
console.log(
  "🔧 Expected tool call string:",
  `${LOCAL_TOOL_CALL_PREFIX}${TOOL_START_GRIEVANCE_REPORT}`
);

/**
 * Initializes or retrieves the chat history and sets up the system instruction.
 * For a local LLM, this mainly means preparing the initial messages array,
 * including the system instruction, if it's a new conversation.
 * @param userProfile The user's profile information.
 */
const initializeChatHistory = (userProfile: UserProfile): void => {
  // If chat history is empty or user profile changed, re-initialize system instruction
  if (
    chatHistory.length === 0 ||
    userProfileCache?.email !== userProfile.email
  ) {
    userProfileCache = userProfile;
    const systemInstruction = `You are a helpful and empathetic assistant named CivicAssist. Your goal is to help the user draft a formal grievance report by first collecting all necessary information.

User Information (auto-detected):
- Name: ${userProfile.firstName} ${userProfile.lastName}
- Email: ${userProfile.email}

Your Primary Task is to conduct a guided conversation to gather details. Do NOT output "${LOCAL_TOOL_CALL_PREFIX}${TOOL_START_GRIEVANCE_REPORT}" until you are confident you have collected enough information to draft a meaningful report.

IMPORTANT: Never show tool calls or technical commands to the user. Keep all interactions natural and conversational.

You MUST collect the following information from the user through conversation:
1.  **Nature of the Grievance:** What is the core issue? (e.g., harassment, unfair treatment, safety concern).
2.  **People Involved:** Who are the key individuals, departments, or entities?
3.  **Incident Description:** A detailed account of what happened.
4.  **Location & Date:** Where and when did the incident(s) occur?
5.  **Desired Resolution:** What outcome is the user seeking?

Only after you have gathered all these details should you take the initiative to indicate opening the drafting canvas. Say something like "Thank you for providing those details. I have enough information to begin your report. I'm opening the drafting canvas now." and then immediately output "${LOCAL_TOOL_CALL_PREFIX}${TOOL_START_GRIEVANCE_REPORT}" on a separate line. Do not ask for permission.

AFTER the report is generated, if the user provides additional information or requests changes, use the UpdateTool instead of regenerating the entire report. For example, if they add new details about the incident, say "I'll add that information to your report" and then output "${LOCAL_TOOL_CALL_PREFIX}${TOOL_UPDATE_REPORT}(section='Detailed Description', content='[new content here]')" to update only the relevant section.

When using UpdateTool, follow this format exactly:
- For adding new content: "${LOCAL_TOOL_CALL_PREFIX}${TOOL_UPDATE_REPORT}(section='Section Name', content='New content to add', action='append')"
- For replacing content: "${LOCAL_TOOL_CALL_PREFIX}${TOOL_UPDATE_REPORT}(section='Section Name', content='Replacement content', action='replace')"

Available sections for updates:
- Contact Details
- Incident Summary
- Detailed Description
- Desired Resolution
- Supporting Evidence
`;
    chatHistory = [{ role: "system", content: systemInstruction }];
    console.log("localLLMService: Chat history initialized.");
  }
};

/**
 * Resets the chat history, effectively starting a new conversation.
 */
export const resetChat = () => {
  chatHistory = [];
  userProfileCache = null;
  console.log("localLLMService: Chat history reset.");
};

/**
 * Converts a Blob (like audio) to a Base64 string.
 * @param blob The Blob to convert.
 * @returns A Promise that resolves with the Base64 string.
 */
const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      if (typeof reader.result === "string") {
        // Remove the data URL prefix (e.g., "data:audio/webm;base64,")
        resolve(reader.result.split(",")[1]);
      } else {
        reject(new Error("Failed to convert blob to base64 string."));
      }
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

/**
 * Streams a chat response from the local LLM.
 * @param message The user's text message.
 * @param userProfile The user's profile information.
 * @param onChunk Callback for each text chunk received.
 * @param onToolCall Callback when a tool call is detected.
 * @param fileData Optional: Object containing mimeType and base64Data for an attached file.
 * @param audioData Optional: Blob containing audio data to be transcribed or sent directly.
 * @returns A Promise that resolves when the stream is complete.
 */
export const streamChatResponse = async (
  message: string,
  userProfile: UserProfile,
  onChunk: (chunk: string) => void,
  onToolCall: () => void,
  fileData?: { mimeType: string; base64Data: string },
  audioData?: Blob
): Promise<void> => {
  console.log(
    "localLLMService: streamChatResponse called with message:",
    message
  );
  console.log("localLLMService: fileData present?", !!fileData);
  console.log("localLLMService: audioData present?", !!audioData);

  // Ensure chat history is initialized with the system instruction
  initializeChatHistory(userProfile);

  let userMessageContent: {
    role: string;
    content?: string;
    images?: string[];
  } = {
    role: "user",
    content: message,
  };

  if (fileData) {
    // For images, Ollama expects 'images' array. For text files, we append to content.
    if (fileData.mimeType.startsWith("image/")) {
      userMessageContent.images = userMessageContent.images || [];
      userMessageContent.images.push(fileData.base64Data);
      console.log("localLLMService: Added image to user message content.");
    } else {
      // For non-image files (e.g., text, markdown), append content to the message
      userMessageContent.content = `${message}\n\n--- Attached File (${
        fileData.mimeType
      }) ---\n${atob(fileData.base64Data)}\n--- End of File ---`;
      console.log(
        "localLLMService: Appended text file content to user message."
      );
    }
  }

  if (audioData) {
    // Convert audio blob to base64 for Ollama
    try {
      await blobToBase64(audioData); // Convert for potential future use
      // Ollama's multimodal support primarily focuses on images.
      // For audio, typically you'd transcribe it first then send text.
      // If Ollama model directly supports audio (e.g., LLaVA models with audio capability),
      // you might pass it in a custom way or as part of the content.
      // For now, let's include it in the text or as a special "image" if supported by the model
      // or assume a pre-transcription step.
      // Given Gemma 3n is multimodal, we *can* pass it as part of content if instructed via prompt.
      // For a robust setup, an STT step *before* sending to LLM is more common for non-vision models.
      // However, since Gemma 3n is multimodal, we'll assume it can process base64 audio if prompted.
      // Ollama's API for true multimodal (beyond LLaVA's image support) might evolve.
      // For now, we simulate passing it as 'image' for testing or recommend STT.
      // IMPORTANT: Ollama's `images` field is specifically for images (JPEG, PNG, GIF).
      // For audio, a separate API for STT is usually called *before* this chat endpoint,
      // or the model itself must explicitly support audio inputs in its `images` array (less common).
      // For robust multimodal audio, a separate STT service is generally needed.
      // Let's modify the prompt to explicitly state audio if present for now.
      userMessageContent.content = `${userMessageContent.content}\n\n--- Attached Audio (Base64 encoded): ---\n(Audio content not directly processed by current Ollama multimodal image input, but metadata sent for context)\n--- End Audio ---`;
      console.log(
        "localLLMService: Noted audio attachment in user message content."
      );
      // If the model actually supports audio via `images` or another field:
      // userMessageContent.images = userMessageContent.images || [];
      // userMessageContent.images.push(base64Audio); // This would be if Ollama's 'images' supported audio mime types
    } catch (error) {
      console.error("Error converting audio to base64:", error);
      onChunk("Error processing audio. Please try again.");
      // Do not return here, let the message send without audio if conversion fails
    }
  }

  // Add the user's message (and potentially file/audio info) to history before sending
  // Note: Storing full base64 images in chat history might make it very large.
  // For production, you might store references or URLs.
  chatHistory.push(userMessageContent);
  console.log(
    "localLLMService: Current chat history sent to Ollama:",
    chatHistory
  );

  try {
    const response = await fetch(`${OLLAMA_API_BASE_URL}/api/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gemma3n:e4b", // Specify your local Gemma 3n 4B model name (updated)
        messages: chatHistory, // Send the full history, including multimodal user input
        stream: true, // Request a streaming response
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `Ollama API error: ${response.status} - ${
          errorData.error || "Unknown error"
        }`
      );
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder("utf-8");
    let assistantResponseContent = ""; // Accumulate assistant's response

    while (true) {
      const { value, done } = await reader?.read()!;
      if (done) {
        console.log("localLLMService: Stream finished.");
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      chunk.split("\n").forEach((line) => {
        if (line.trim() === "") return;
        try {
          const data = JSON.parse(line);
          if (data.done === false) {
            // This indicates a regular content chunk
            const content = data.message?.content || "";
            console.log("🔧 Received content chunk:", JSON.stringify(content));
            if (content) {
              if (content.includes(LOCAL_TOOL_CALL_PREFIX)) {
                console.log("🔧 Tool call detected in content:", content);
                // The LLM might output text before the tool call.
                // We need to split correctly and handle potential newlines/spaces.
                const toolCallIndex = content.indexOf(LOCAL_TOOL_CALL_PREFIX);
                const textPart = content.substring(0, toolCallIndex).trim(); // Trim text part
                const toolCallPart = content.substring(toolCallIndex); // Keep tool call part as is for startsWith check

                console.log("🔧 Text part:", textPart);
                console.log("🔧 Tool call part:", toolCallPart);

                if (textPart) {
                  onChunk(textPart); // Emit text before the tool call
                  assistantResponseContent += textPart;
                }

                if (toolCallPart.startsWith(TOOL_START_GRIEVANCE_REPORT)) {
                  console.log("🔧 startGrievanceReport tool call triggered!");
                  onToolCall(); // Trigger the tool call
                } else if (toolCallPart.startsWith(TOOL_UPDATE_REPORT)) {
                  console.log("🔧 updateReport tool call triggered!");
                  // Enhanced parsing for UpdateTool with action parameter
                  const updateMatch = toolCallPart.match(
                    /updateReport\(section='([^']+)', content='([^']+)'(?:, action='([^']+)')?\)/
                  );
                  if (updateMatch) {
                    const [, section, content, action = "append"] = updateMatch;
                    console.log(
                      "🔧 Update section:",
                      section,
                      "Content:",
                      content,
                      "Action:",
                      action
                    );

                    // Store update data for the callback
                    (onToolCall as any).updateData = {
                      section,
                      content,
                      action,
                    };
                    onToolCall();
                  } else {
                    console.log(
                      "🔧 Failed to parse updateReport tool call:",
                      toolCallPart
                    );
                  }
                } else {
                  console.log(
                    "🔧 Tool call part does not match any known tools:",
                    toolCallPart
                  );
                  onChunk(toolCallPart); // Emit unknown tool call as text
                  assistantResponseContent += toolCallPart;
                }
              } else {
                onChunk(content);
                assistantResponseContent += content;
              }
            }
          } else if (data.done === true) {
            // End of stream, add the assistant's full response to history
            console.log(
              "🔧 Final accumulated response:",
              assistantResponseContent
            );

            // Check if tool call is in the final accumulated response (in case it was split across chunks)
            if (
              assistantResponseContent.includes(
                `${LOCAL_TOOL_CALL_PREFIX}${TOOL_START_GRIEVANCE_REPORT}`
              )
            ) {
              console.log(
                "🔧 startGrievanceReport tool call found in final accumulated response!"
              );
              onToolCall();
            } else if (
              assistantResponseContent.includes(
                `${LOCAL_TOOL_CALL_PREFIX}${TOOL_UPDATE_REPORT}`
              )
            ) {
              console.log(
                "🔧 updateReport tool call found in final accumulated response!"
              );
              onToolCall();
            }

            chatHistory.push({
              role: "assistant",
              content: assistantResponseContent.trim(),
            });
            console.log(
              "localLLMService: Assistant's final response added to history."
            );
          }
        } catch (e) {
          console.error(
            "Error parsing Ollama stream chunk:",
            e,
            "Chunk:",
            line
          );
        }
      });
    }
  } catch (error) {
    console.error("Error streaming chat response from local LLM:", error);
    onChunk(
      `Sorry, I encountered an error with the local LLM: ${
        error instanceof Error ? error.message : String(error)
      }. Please ensure the Ollama server is running and the 'gemma3n:e4b' model is available.`
    );
    // Revert the last user message from history if an error occurred before a full response
    chatHistory.pop();
  }
};

/**
 * Generates a formal grievance report in Markdown format using the local LLM.
 * This function also sends the full chat history, which implicitly includes multimodal inputs
 * that were added to the chat history during streamChatResponse calls.
 * @param chatHistory The full chat history leading to the report generation.
 * @param userProfile The user's profile information.
 * @param currentReport Optional: The current draft of the report to refine.
 * @returns A Promise that resolves with the generated Markdown report.
 */
export const generateReport = async (
  chatHistory: Message[],
  userProfile: UserProfile,
  currentReport?: string
): Promise<string> => {
  console.log("localLLMService: generateReport called.");
  try {
    // Map chat history to the format expected by Ollama's /api/generate context
    const conversationForPrompt = chatHistory.map((msg) => {
      const chatMessage: { role: string; content: string; images?: string[] } =
        {
          role: msg.role === Role.User ? "user" : "assistant",
          content: msg.text,
        };
      if (msg.imageData) {
        chatMessage.images = [msg.imageData.split(",")[1]]; // Send only base64 part
      }
      return chatMessage;
    });

    let promptContent;

    if (currentReport && currentReport.trim() !== "") {
      promptContent = `Based on the latest user request in the conversation history, please refine the following Markdown grievance report.
- Your response MUST be only the full, updated Markdown content. Do not include any extra text, explanations, or markdown fences like \`\`\`markdown.
- Only insert or modify the content in the relevant section as requested by the user. The rest of the document MUST remain unchanged.
- Ensure the final output is a single, complete block of valid Markdown.

---Conversation History---
${conversationForPrompt
  .map((msg) => {
    const imgPart =
      msg.images && msg.images.length > 0
        ? ` [Image: ${msg.images.length} attached]`
        : "";
    return `${msg.role}: ${msg.content}${imgPart}`;
  })
  .join("\n")}

---Current Markdown Draft to Refine---
${currentReport}

---End of Data---`;
    } else {
      promptContent = `Based on the conversation history, draft a formal grievance report in Markdown.

**Formatting Rules:**
- The response MUST be ONLY the Markdown content, with no extra text or markdown fences.
- Use the exact structure and placeholders provided below.
- Fill in the user's information from their profile.
- Infer other details from the conversation.

**User Profile:**
- Full Name: ${userProfile.firstName} ${userProfile.lastName}
- Email: ${userProfile.email}
- Phone: [Your Contact Number]

**Markdown Template:**
## Formal Grievance Report

**Date:** ${new Date().toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })}
**From:** ${userProfile.firstName} ${userProfile.lastName}
**To:** [Appropriate Department, e.g., Human Resources]

### Contact Details
- **Email:** ${userProfile.email}
- **Phone:** [Your Contact Number]
- **Department:** [Your Department, if applicable]

### Incident Summary
- **Nature of Grievance:** [Briefly state the core issue, e.g., Workplace Harassment]
- **Person(s) Involved:** [List names and titles, or "Unknown" if not applicable]
- **Date of Incident(s):** [Date or Date Range]
- **Location of Incident(s):** [Specific location, e.g., Office, Floor, Online]

### Detailed Description
[Provide a clear, chronological, and factual account of the events. Use bullet points for multiple events if it improves clarity.]

### Desired Resolution
[Clearly state the outcome or action you are seeking to resolve this grievance.]

---
*This report was generated with the assistance of an AI drafting tool. Please review for accuracy before submitting.*

---Conversation to Analyze---
${conversationForPrompt
  .map((msg) => {
    const imgPart =
      msg.images && msg.images.length > 0
        ? ` [Image: ${msg.images.length} attached]`
        : "";
    return `${msg.role}: ${msg.content}${imgPart}`;
  })
  .join("\n")}
---End Conversation---`;
    }
    console.log(
      "localLLMService: Sending prompt to /api/generate:\n",
      promptContent
    );

    const requestBody = {
      model: "gemma3n:e4b", // Specify your local Gemma 3n 4B model name (updated)
      prompt: promptContent,
      stream: false, // We want the full response at once
      // Note: Ollama's /api/generate for single prompt doesn't typically take 'images' in same way as /api/chat messages.
      // Multimodal input for generation is usually done by including image data in the prompt itself if it's a vision model.
      // However, since we're generating a report based on history, the images should already be "understood" by the model
      // from the previous chat turns if they were passed via /api/chat.
      // If you explicitly needed to pass an image for report generation, the promptContent would need to refer to it,
      // and Ollama's `/api/generate` might require a specific JSON structure for image-only prompts.
      // For now, we rely on the model's memory from the chat history.
    };

    // Debug logging - remove in production
    console.log("🔧 Ollama Generate Request:", {
      url: `${OLLAMA_API_BASE_URL}/api/generate`,
      method: "POST",
      model: requestBody.model,
      promptLength: promptContent.length,
    });

    const response = await fetch(`${OLLAMA_API_BASE_URL}/api/generate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (parseError) {
        errorData = { error: "Could not parse error response" };
      }

      // Enhanced error logging
      console.error("🟥 Ollama Generate API Error Details:", {
        status: response.status,
        statusText: response.statusText,
        url: response.url,
        errorData: errorData,
      });

      // Provide specific error messages based on status code
      let errorMessage = `Ollama Generate API error: ${response.status}`;
      if (response.status === 404) {
        errorMessage +=
          ` - Model '${requestBody.model}' not found. Please check:\n` +
          `1. Run 'ollama list' to see available models\n` +
          `2. Ensure model name matches exactly (case-sensitive)\n` +
          `3. Pull the model if needed: 'ollama pull ${requestBody.model}'`;
      } else if (response.status === 500) {
        errorMessage += ` - Server error. Check if Ollama is running: 'ollama serve'`;
      } else {
        errorMessage += ` - ${errorData.error || "Unknown error"}`;
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();
    let markdownResponse = data.response.trim(); // Ollama's /api/generate returns 'response' field
    console.log(
      "localLLMService: Raw report generation response:",
      markdownResponse
    );

    // Remove markdown fences if present
    const markdownFenceRegex = /^```markdown\s*\n?(.*?)\n?\s*```$/s;
    const match = markdownResponse.match(markdownFenceRegex);
    if (match && match[1]) {
      markdownResponse = match[1].trim();
      console.log("localLLMService: Removed markdown fences from report.");
    }

    return markdownResponse;
  } catch (error) {
    console.error(
      "localLLMService: Error generating report from local LLM:",
      error
    );
    return `### Error\n\nCould not generate the report from the local LLM: ${
      error instanceof Error ? error.message : String(error)
    }. Please try again or ensure the server is running.`;
  }
};

/**
 * Debug function to test Ollama integration
 * Call this from browser console: window.debugOllama()
 */
export const debugOllama = async (
  modelName: string = "gemma3n:e4b"
): Promise<void> => {
  logOllamaConfig();
  await runOllamaDebugSuite(modelName);
};

// Make debug function available globally for easy testing
if (typeof window !== "undefined") {
  (window as any).debugOllama = debugOllama;
}
