import React, { useEffect, useState, memo } from 'react';
import './SpaceLogo.css';

interface SpaceLogoProps {
  className?: string; // Add className prop
}

const SpaceLogo: React.FC<SpaceLogoProps> = ({ className }) => { // Destructure className
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`space-logo-container ${isLoaded ? 'loaded' : ''} ${className || ''}`}> {/* Apply className */}
      {/* Outer glow effect */}
      <div className="space-logo-glow"></div>

      {/* Orbital rings */}
      <div className="space-logo-orbital-ring ring-1"></div>
      <div className="space-logo-orbital-ring ring-2"></div>
      <div className="space-logo-orbital-ring ring-3"></div>

      {/* Main logo container */}
      <div className="space-logo-outer">
        <div className="space-logo-inner">
          <div className="space-logo-core">
            <span className="logo-text">37</span>
          </div>
        </div>
      </div>

      {/* Floating particles */}
      <div className="space-logo-particles">
        {[...Array(8)].map((_, i) => (
          <div key={i} className={`space-logo-particle particle-${i + 1}`}></div>
        ))}
      </div>

      {/* Energy beams */}
      <div className="space-logo-beams">
        <div className="beam beam-1"></div>
        <div className="beam beam-2"></div>
        <div className="beam beam-3"></div>
        <div className="beam beam-4"></div>
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(SpaceLogo);
