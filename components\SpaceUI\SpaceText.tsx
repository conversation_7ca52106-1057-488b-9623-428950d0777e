import React, { useEffect, useState, useMemo } from "react";
import "./SpaceText.css";

interface SpaceTextProps {
  question?: string; // Make optional as it's not always used by itself
  description?: string; // Make optional
  type?: "h1" | "h2" | "p" | "span"; // Add type to specify HTML tag
  className?: string; // Add className for external styling
  children?: React.ReactNode; // Allow children for more flexible content
}

const SpaceText: React.FC<SpaceTextProps> = ({
  question,
  description,
  type: TextTag = "p",
  className,
  children,
}) => {
  const [words, setWords] = useState<string[]>([]);

  // Determine the text to animate: prioritize children, then question+description, then question, then description.
  const textToAnimate = useMemo(() => {
    if (children) {
      if (typeof children === "string") return children;
      if (typeof children === "number") return children.toString();
      // For other types, try to extract text content
      return String(children);
    }
    if (question && description) return `${question} ${description}`;
    if (question) return question;
    if (description) return description;
    return "";
  }, [question, description, children]);

  useEffect(() => {
    setWords(textToAnimate.split(" "));
  }, [textToAnimate]);

  return (
    <TextTag className={className}>
      {words.map((word, index) => (
        <span
          key={index} // Use index as key, or better, unique ID if words could reorder. For simple text, index is fine.
          className="space-text-word"
          style={{
            animationDelay: `${1.4 + index * 0.03}s`,
          }}
        >
          {word}{" "}
        </span>
      ))}
    </TextTag>
  );
};

export default SpaceText;
